from django.contrib import admin
from .models import Post, Search, Result, User
from django.contrib.auth.admin import UserAdmin

admin.site.register(User, UserAdmin) 

class ResultInline(admin.StackedInline):
    model = Result

@admin.register(Search)
class SearchAdmin(admin.ModelAdmin):
    list_display = ['query', 'created', 'updated']
    inlines = [ResultInline]

class ResultAdmin(admin.ModelAdmin):
    list_display = ['pk','query','image', 'created', 'updated']

@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    list_display = ['__str__', 'created', 'updated']

