from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import Post, Search, Result, User


admin.site.register(User, BaseUserAdmin)


class ResultInline(admin.TabularInline):
    """Inline admin for Results."""
    model = Result
    extra = 0
    readonly_fields = ['created', 'updated']
    fields = ['image', 'dis_image', 'created']


@admin.register(Search)
class SearchAdmin(admin.ModelAdmin):
    """Enhanced Search admin with better display and filtering."""
    list_display = ['query', 'result_count', 'created', 'updated']
    list_filter = ['created', 'updated']
    search_fields = ['query']
    ordering = ['-updated']
    readonly_fields = ['created', 'updated']
    inlines = [ResultInline]

    def result_count(self, obj):
        """Display the number of results for this search."""
        return obj.results.count()
    result_count.short_description = 'Results'


@admin.register(Result)
class ResultAdmin(admin.ModelAdmin):
    """Enhanced Result admin with better display and filtering."""
    list_display = ['id', 'query', 'image_preview', 'created']
    list_filter = ['created', 'query']
    search_fields = ['query__query']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    def image_preview(self, obj):
        """Display a small preview of the image."""
        if obj.dis_image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.dis_image
            )
        return "No preview"
    image_preview.short_description = 'Preview'


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """Enhanced Post admin with better display and filtering."""
    list_display = ['id', 'user', 'posted', 'image_preview', 'created']
    list_filter = ['posted', 'created', 'user']
    search_fields = ['user__username', 'caption', 'post_text', 'hashtags']
    ordering = ['-created']
    readonly_fields = ['created', 'updated', 'image_preview']

    fieldsets = (
        ('Basic Info', {
            'fields': ('user', 'posted')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'caption', 'post_text', 'hashtags')
        }),
        ('Instagram Info', {
            'fields': ('published_image_id',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        """Display a small preview of the post image."""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 150px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'

    actions = ['mark_as_posted', 'mark_as_draft']

    def mark_as_posted(self, request, queryset):
        """Mark selected posts as posted."""
        updated = queryset.update(posted=True)
        self.message_user(request, f'{updated} posts marked as posted.')
    mark_as_posted.short_description = 'Mark selected posts as posted'

    def mark_as_draft(self, request, queryset):
        """Mark selected posts as draft."""
        updated = queryset.update(posted=False)
        self.message_user(request, f'{updated} posts marked as draft.')
    mark_as_draft.short_description = 'Mark selected posts as draft'


# Customize admin site header and title
admin.site.site_header = 'Imagsta Administration'
admin.site.site_title = 'Imagsta Admin'
admin.site.index_title = 'Welcome to Imagsta Administration'

