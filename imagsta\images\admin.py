from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as BaseUserAdmin
from django.utils.html import format_html
from .models import Post, Search, Result, User


@admin.register(User)
class UserAdmin(BaseUserAdmin):
    """Enhanced User admin with additional fields."""
    list_display = ['username', 'email', 'first_name', 'last_name', 'is_staff', 'created_at']
    list_filter = ['is_staff', 'is_superuser', 'is_active', 'created_at']
    search_fields = ['username', 'email', 'first_name', 'last_name']
    ordering = ['-created_at']

    fieldsets = BaseUserAdmin.fieldsets + (
        ('Additional Info', {'fields': ('created_at', 'updated_at')}),
    )
    readonly_fields = ['created_at', 'updated_at']


class ResultInline(admin.TabularInline):
    """Inline admin for Results."""
    model = Result
    extra = 0
    readonly_fields = ['id', 'created', 'updated']
    fields = ['image_alt', 'source', 'image', 'dis_image', 'created']


@admin.register(Search)
class SearchAdmin(admin.ModelAdmin):
    """Enhanced Search admin with better display and filtering."""
    list_display = ['query', 'search_count', 'result_count', 'created', 'updated']
    list_filter = ['created', 'updated', 'search_count']
    search_fields = ['query']
    ordering = ['-updated']
    readonly_fields = ['created', 'updated']
    inlines = [ResultInline]

    def result_count(self, obj):
        """Display the number of results for this search."""
        return obj.results.count()
    result_count.short_description = 'Results'


@admin.register(Result)
class ResultAdmin(admin.ModelAdmin):
    """Enhanced Result admin with better display and filtering."""
    list_display = ['id', 'query', 'source', 'image_preview', 'created']
    list_filter = ['source', 'created', 'query']
    search_fields = ['query__query', 'image_alt']
    ordering = ['-created']
    readonly_fields = ['id', 'created', 'updated', 'image_preview']

    def image_preview(self, obj):
        """Display a small preview of the image."""
        if obj.dis_image:
            return format_html(
                '<img src="{}" style="max-width: 100px; max-height: 100px;" />',
                obj.dis_image
            )
        return "No preview"
    image_preview.short_description = 'Preview'


@admin.register(Post)
class PostAdmin(admin.ModelAdmin):
    """Enhanced Post admin with better display and filtering."""
    list_display = ['id', 'user', 'status', 'image_preview', 'scheduled_for', 'created']
    list_filter = ['status', 'created', 'scheduled_for', 'user']
    search_fields = ['user__username', 'caption', 'post_text', 'hashtags']
    ordering = ['-created']
    readonly_fields = ['id', 'created', 'updated', 'image_preview']

    fieldsets = (
        ('Basic Info', {
            'fields': ('user', 'status', 'scheduled_for')
        }),
        ('Content', {
            'fields': ('image', 'image_preview', 'caption', 'post_text', 'hashtags')
        }),
        ('Instagram Info', {
            'fields': ('published_image_id',),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created', 'updated'),
            'classes': ('collapse',)
        }),
    )

    def image_preview(self, obj):
        """Display a small preview of the post image."""
        if obj.image:
            return format_html(
                '<img src="{}" style="max-width: 150px; max-height: 150px;" />',
                obj.image.url
            )
        return "No image"
    image_preview.short_description = 'Preview'

    actions = ['mark_as_ready', 'mark_as_draft']

    def mark_as_ready(self, request, queryset):
        """Mark selected posts as ready to post."""
        updated = queryset.update(status='ready')
        self.message_user(request, f'{updated} posts marked as ready.')
    mark_as_ready.short_description = 'Mark selected posts as ready'

    def mark_as_draft(self, request, queryset):
        """Mark selected posts as draft."""
        updated = queryset.update(status='draft')
        self.message_user(request, f'{updated} posts marked as draft.')
    mark_as_draft.short_description = 'Mark selected posts as draft'


# Customize admin site header and title
admin.site.site_header = 'Imagsta Administration'
admin.site.site_title = 'Imagsta Admin'
admin.site.index_title = 'Welcome to Imagsta Administration'

