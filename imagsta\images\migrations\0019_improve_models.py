# Generated manually for model improvements

from django.db import migrations, models
import django.core.validators
import uuid


class Migration(migrations.Migration):

    dependencies = [
        ('images', '0018_result_dis_image'),
    ]

    operations = [
        # Add new fields to User model
        migrations.AddField(
            model_name='user',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, null=True),
        ),
        migrations.AddField(
            model_name='user',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, null=True),
        ),
        
        # Add search_count to Search model
        migrations.AddField(
            model_name='search',
            name='search_count',
            field=models.PositiveIntegerField(default=1),
        ),
        
        # Add new fields to Result model
        migrations.AddField(
            model_name='result',
            name='image_alt',
            field=models.CharField(blank=True, max_length=255),
        ),
        migrations.AddField(
            model_name='result',
            name='source',
            field=models.CharField(default='unsplash', max_length=50),
        ),
        
        # Update Post model fields
        migrations.AddField(
            model_name='post',
            name='status',
            field=models.CharField(
                choices=[
                    ('draft', 'Draft'),
                    ('ready', 'Ready to Post'),
                    ('posted', 'Posted'),
                    ('failed', 'Failed to Post'),
                ],
                default='draft',
                max_length=10
            ),
        ),
        migrations.AddField(
            model_name='post',
            name='scheduled_for',
            field=models.DateTimeField(blank=True, null=True),
        ),
        
        # Update field constraints
        migrations.AlterField(
            model_name='search',
            name='query',
            field=models.CharField(
                max_length=128,
                unique=True,
                validators=[django.core.validators.MinLengthValidator(2)]
            ),
        ),
        migrations.AlterField(
            model_name='post',
            name='post_text',
            field=models.TextField(blank=True, max_length=2200),
        ),
        migrations.AlterField(
            model_name='post',
            name='image',
            field=models.ImageField(upload_to='images/posts/%Y/%m/%d/'),
        ),
        
        # Remove the old 'posted' field from Post model
        migrations.RemoveField(
            model_name='post',
            name='posted',
        ),
    ]
