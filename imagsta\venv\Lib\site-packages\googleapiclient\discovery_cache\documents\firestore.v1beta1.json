{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}, "https://www.googleapis.com/auth/datastore": {"description": "View and manage your Google Cloud Datastore data"}}}}, "basePath": "", "baseUrl": "https://firestore.googleapis.com/", "batchPath": "batch", "canonicalName": "Firestore", "description": "Accesses the NoSQL document database built for automatic scaling, high performance, and ease of application development. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/firestore", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "firestore:v1beta1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://firestore.mtls.googleapis.com/", "name": "firestore", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"databases": {"methods": {"exportDocuments": {"description": "Exports a copy of all or a subset of documents from Google Cloud Firestore to another storage system, such as Google Cloud Storage. Recent updates to documents may not be reflected in the export. The export occurs in the background and its progress can be monitored and managed via the Operation resource that is created. The output of an export may only be used once the associated operation is done. If an export operation is cancelled before completion it may leave partial data behind in Google Cloud Storage.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}:exportDocuments", "httpMethod": "POST", "id": "firestore.projects.databases.exportDocuments", "parameterOrder": ["name"], "parameters": {"name": {"description": "Database to export. Should be of the form: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:exportDocuments", "request": {"$ref": "GoogleFirestoreAdminV1beta1ExportDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "importDocuments": {"description": "Imports documents into Google Cloud Firestore. Existing documents with the same name are overwritten. The import occurs in the background and its progress can be monitored and managed via the Operation resource that is created. If an ImportDocuments operation is cancelled, it is possible that a subset of the data has already been imported to Cloud Firestore.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}:importDocuments", "httpMethod": "POST", "id": "firestore.projects.databases.importDocuments", "parameterOrder": ["name"], "parameters": {"name": {"description": "Database to import into. Should be of the form: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}:importDocuments", "request": {"$ref": "GoogleFirestoreAdminV1beta1ImportDocumentsRequest"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}}, "resources": {"documents": {"methods": {"batchGet": {"description": "Gets multiple documents. Documents returned by this method are not guaranteed to be returned in the same order that they were requested.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:batchGet", "httpMethod": "POST", "id": "firestore.projects.databases.documents.batchGet", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:batchGet", "request": {"$ref": "BatchGetDocumentsRequest"}, "response": {"$ref": "BatchGetDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "batchWrite": {"description": "Applies a batch of write operations. The BatchWrite method does not apply the write operations atomically and can apply them out of order. Method does not allow more than one write per document. Each write succeeds or fails independently. See the BatchWriteResponse for the success status of each write. If you require an atomically applied set of writes, use Commit instead.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:batchWrite", "httpMethod": "POST", "id": "firestore.projects.databases.documents.batchWrite", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:batchWrite", "request": {"$ref": "BatchWriteRequest"}, "response": {"$ref": "BatchWriteResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "beginTransaction": {"description": "Starts a new transaction.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:beginTransaction", "httpMethod": "POST", "id": "firestore.projects.databases.documents.beginTransaction", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:beginTransaction", "request": {"$ref": "BeginTransactionRequest"}, "response": {"$ref": "BeginTransactionResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "commit": {"description": "Commits a transaction, while optionally updating documents.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:commit", "httpMethod": "POST", "id": "firestore.projects.databases.documents.commit", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:commit", "request": {"$ref": "CommitRequest"}, "response": {"$ref": "CommitResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "createDocument": {"description": "Creates a new document.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{collectionId}", "httpMethod": "POST", "id": "firestore.projects.databases.documents.createDocument", "parameterOrder": ["parent", "collectionId"], "parameters": {"collectionId": {"description": "Required. The collection ID, relative to `parent`, to list. For example: `chatrooms`.", "location": "path", "required": true, "type": "string"}, "documentId": {"description": "The client-assigned document ID to use for this document. Optional. If not specified, an ID will be assigned by the service.", "location": "query", "type": "string"}, "mask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}, "parent": {"description": "Required. The parent resource. For example: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/chatrooms/{chatroom_id}`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/{collectionId}", "request": {"$ref": "Document"}, "response": {"$ref": "Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "delete": {"description": "Deletes a document.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}", "httpMethod": "DELETE", "id": "firestore.projects.databases.documents.delete", "parameterOrder": ["name"], "parameters": {"currentDocument.exists": {"description": "When set to `true`, the target document must exist. When set to `false`, the target document must not exist.", "location": "query", "type": "boolean"}, "currentDocument.updateTime": {"description": "When set, the target document must exist and have been last updated at that time. Timestamp must be microsecond aligned.", "format": "google-datetime", "location": "query", "type": "string"}, "name": {"description": "Required. The resource name of the Document to delete. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "get": {"description": "Gets a single document.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}", "httpMethod": "GET", "id": "firestore.projects.databases.documents.get", "parameterOrder": ["name"], "parameters": {"mask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}, "name": {"description": "Required. The resource name of the Document to get. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}, "readTime": {"description": "Reads the version of the document at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "location": "query", "type": "string"}, "transaction": {"description": "Reads the document in a transaction.", "format": "byte", "location": "query", "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "list": {"description": "Lists documents.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}/{collectionId}", "httpMethod": "GET", "id": "firestore.projects.databases.documents.list", "parameterOrder": ["parent", "collectionId"], "parameters": {"collectionId": {"description": "Optional. The collection ID, relative to `parent`, to list. For example: `chatrooms` or `messages`. This is optional, and when not provided, Firestore will list documents from all collections under the provided `parent`.", "location": "path", "required": true, "type": "string"}, "mask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}, "orderBy": {"description": "Optional. The optional ordering of the documents to return. For example: `priority desc, __name__ desc`. This mirrors the `ORDER BY` used in Firestore queries but in a string representation. When absent, documents are ordered based on `__name__ ASC`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of documents to return in a single response. Firestore may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDocuments` response. Provide this to retrieve the subsequent page. When paginating, all other parameters (with the exception of `page_size`) must match the values set in the request that generated the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents` or `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}, "readTime": {"description": "Perform the read at the provided time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "location": "query", "type": "string"}, "showMissing": {"description": "If the list should show missing documents. A document is missing if it does not exist, but there are sub-documents nested underneath it. When true, such missing documents will be returned with a key but will not have fields, `create_time`, or `update_time` set. Requests with `show_missing` may not specify `where` or `order_by`.", "location": "query", "type": "boolean"}, "transaction": {"description": "Perform the read as part of an already active transaction.", "format": "byte", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/{collectionId}", "response": {"$ref": "ListDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "listCollectionIds": {"description": "Lists all the collection IDs underneath a document.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:listCollectionIds", "httpMethod": "POST", "id": "firestore.projects.databases.documents.listCollectionIds", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent document. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:listCollectionIds", "request": {"$ref": "ListCollectionIdsRequest"}, "response": {"$ref": "ListCollectionIdsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "listDocuments": {"description": "Lists documents.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{collectionId}", "httpMethod": "GET", "id": "firestore.projects.databases.documents.listDocuments", "parameterOrder": ["parent", "collectionId"], "parameters": {"collectionId": {"description": "Optional. The collection ID, relative to `parent`, to list. For example: `chatrooms` or `messages`. This is optional, and when not provided, Firestore will list documents from all collections under the provided `parent`.", "location": "path", "required": true, "type": "string"}, "mask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}, "orderBy": {"description": "Optional. The optional ordering of the documents to return. For example: `priority desc, __name__ desc`. This mirrors the `ORDER BY` used in Firestore queries but in a string representation. When absent, documents are ordered based on `__name__ ASC`.", "location": "query", "type": "string"}, "pageSize": {"description": "Optional. The maximum number of documents to return in a single response. Firestore may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListDocuments` response. Provide this to retrieve the subsequent page. When paginating, all other parameters (with the exception of `page_size`) must match the values set in the request that generated the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents` or `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents$", "required": true, "type": "string"}, "readTime": {"description": "Perform the read at the provided time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "location": "query", "type": "string"}, "showMissing": {"description": "If the list should show missing documents. A document is missing if it does not exist, but there are sub-documents nested underneath it. When true, such missing documents will be returned with a key but will not have fields, `create_time`, or `update_time` set. Requests with `show_missing` may not specify `where` or `order_by`.", "location": "query", "type": "boolean"}, "transaction": {"description": "Perform the read as part of an already active transaction.", "format": "byte", "location": "query", "type": "string"}}, "path": "v1beta1/{+parent}/{collectionId}", "response": {"$ref": "ListDocumentsResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "listen": {"description": "Listens to changes. This method is only available via gRPC or WebChannel (not REST).", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:listen", "httpMethod": "POST", "id": "firestore.projects.databases.documents.listen", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:listen", "request": {"$ref": "ListenRequest"}, "response": {"$ref": "ListenResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "partitionQuery": {"description": "Partitions a query by returning partition cursors that can be used to run the query in parallel. The returned partition cursors are split points that can be used by RunQuery as starting/end points for the query results.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:partitionQuery", "httpMethod": "POST", "id": "firestore.projects.databases.documents.partitionQuery", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents`. Document resource names are not supported; only database resource names can be specified.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:partitionQuery", "request": {"$ref": "PartitionQueryRequest"}, "response": {"$ref": "PartitionQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "patch": {"description": "Updates or inserts a document.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}", "httpMethod": "PATCH", "id": "firestore.projects.databases.documents.patch", "parameterOrder": ["name"], "parameters": {"currentDocument.exists": {"description": "When set to `true`, the target document must exist. When set to `false`, the target document must not exist.", "location": "query", "type": "boolean"}, "currentDocument.updateTime": {"description": "When set, the target document must exist and have been last updated at that time. Timestamp must be microsecond aligned.", "format": "google-datetime", "location": "query", "type": "string"}, "mask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}, "name": {"description": "The resource name of the document, for example `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}, "updateMask.fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "location": "query", "repeated": true, "type": "string"}}, "path": "v1beta1/{+name}", "request": {"$ref": "Document"}, "response": {"$ref": "Document"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "rollback": {"description": "Rolls back a transaction.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:rollback", "httpMethod": "POST", "id": "firestore.projects.databases.documents.rollback", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:rollback", "request": {"$ref": "RollbackRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "runAggregationQuery": {"description": "Runs an aggregation query. Rather than producing Document results like Firestore.RunQuery, this API allows running an aggregation to produce a series of AggregationResult server-side. High-Level Example: ``` -- Return the number of documents in table given a filter. SELECT COUNT(*) FROM ( SELECT * FROM k where a = true ); ```", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:runAggregationQuery", "httpMethod": "POST", "id": "firestore.projects.databases.documents.runAggregationQuery", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents` or `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:runAggregationQuery", "request": {"$ref": "RunAggregationQueryRequest"}, "response": {"$ref": "RunAggregationQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "runQuery": {"description": "Runs a query.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents/{documentsId}/{documentsId1}:runQuery", "httpMethod": "POST", "id": "firestore.projects.databases.documents.runQuery", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents` or `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/documents/[^/]+/.*$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}:runQuery", "request": {"$ref": "RunQueryRequest"}, "response": {"$ref": "RunQueryResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "write": {"description": "Streams batches of document updates and deletes, in order. This method is only available via gRPC or WebChannel (not REST).", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/documents:write", "httpMethod": "POST", "id": "firestore.projects.databases.documents.write", "parameterOrder": ["database"], "parameters": {"database": {"description": "Required. The database name. In the format: `projects/{project_id}/databases/{database_id}`. This is only required in the first message.", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+database}/documents:write", "request": {"$ref": "WriteRequest"}, "response": {"$ref": "WriteResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}}}, "indexes": {"methods": {"create": {"description": "Creates the specified index. A newly created index's initial state is `CREATING`. On completion of the returned google.longrunning.Operation, the state will be `READY`. If the index already exists, the call will return an `ALREADY_EXISTS` status. During creation, the process could result in an error, in which case the index will move to the `ERROR` state. The process can be recovered by fixing the data that caused the error, removing the index with delete, then re-creating the index with create. Indexes with a single field cannot be created.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/indexes", "httpMethod": "POST", "id": "firestore.projects.databases.indexes.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "The name of the database this index will apply to. For example: `projects/{project_id}/databases/{database_id}`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/indexes", "request": {"$ref": "GoogleFirestoreAdminV1beta1Index"}, "response": {"$ref": "GoogleLongrunningOperation"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "delete": {"description": "Deletes an index.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/indexes/{indexesId}", "httpMethod": "DELETE", "id": "firestore.projects.databases.indexes.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "The index name. For example: `projects/{project_id}/databases/{database_id}/indexes/{index_id}`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/indexes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "get": {"description": "Gets an index.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/indexes/{indexesId}", "httpMethod": "GET", "id": "firestore.projects.databases.indexes.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "The name of the index. For example: `projects/{project_id}/databases/{database_id}/indexes/{index_id}`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+/indexes/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+name}", "response": {"$ref": "GoogleFirestoreAdminV1beta1Index"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}, "list": {"description": "Lists the indexes that match the specified filters.", "flatPath": "v1beta1/projects/{projectsId}/databases/{databasesId}/indexes", "httpMethod": "GET", "id": "firestore.projects.databases.indexes.list", "parameterOrder": ["parent"], "parameters": {"filter": {"location": "query", "type": "string"}, "pageSize": {"description": "The standard List page size.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "The standard List page token.", "location": "query", "type": "string"}, "parent": {"description": "The database name. For example: `projects/{project_id}/databases/{database_id}`", "location": "path", "pattern": "^projects/[^/]+/databases/[^/]+$", "required": true, "type": "string"}}, "path": "v1beta1/{+parent}/indexes", "response": {"$ref": "GoogleFirestoreAdminV1beta1ListIndexesResponse"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform", "https://www.googleapis.com/auth/datastore"]}}}}}}}}, "revision": "20250618", "rootUrl": "https://firestore.googleapis.com/", "schemas": {"Aggregation": {"description": "Defines an aggregation that produces a single result.", "id": "Aggregation", "properties": {"alias": {"description": "Optional. Optional name of the field to store the result of the aggregation into. If not provided, Firestore will pick a default name following the format `field_`. For example: ``` AGGREGATE COUNT_UP_TO(1) AS count_up_to_1, COUNT_UP_TO(2), COUNT_UP_TO(3) AS count_up_to_3, COUNT(*) OVER ( ... ); ``` becomes: ``` AGGREGATE COUNT_UP_TO(1) AS count_up_to_1, COUNT_UP_TO(2) AS field_1, COUNT_UP_TO(3) AS count_up_to_3, COUNT(*) AS field_2 OVER ( ... ); ``` Requires: * Must be unique across all aggregation aliases. * Conform to document field name limitations.", "type": "string"}, "avg": {"$ref": "Avg", "description": "Average aggregator."}, "count": {"$ref": "Count", "description": "Count aggregator."}, "sum": {"$ref": "Sum", "description": "Sum aggregator."}}, "type": "object"}, "AggregationResult": {"description": "The result of a single bucket from a Firestore aggregation query. The keys of `aggregate_fields` are the same for all results in an aggregation query, unlike document queries which can have different fields present for each result.", "id": "AggregationResult", "properties": {"aggregateFields": {"additionalProperties": {"$ref": "Value"}, "description": "The result of the aggregation functions, ex: `COUNT(*) AS total_docs`. The key is the alias assigned to the aggregation function on input and the size of this map equals the number of aggregation functions in the query.", "type": "object"}}, "type": "object"}, "ArrayValue": {"description": "An array value.", "id": "ArrayValue", "properties": {"values": {"description": "Values in the array.", "items": {"$ref": "Value"}, "type": "array"}}, "type": "object"}, "Avg": {"description": "Average of the values of the requested field. * Only numeric values will be aggregated. All non-numeric values including `NULL` are skipped. * If the aggregated values contain `NaN`, returns `NaN`. Infinity math follows IEEE-754 standards. * If the aggregated value set is empty, returns `NULL`. * Always returns the result as a double.", "id": "Avg", "properties": {"field": {"$ref": "FieldReference", "description": "The field to aggregate on."}}, "type": "object"}, "BatchGetDocumentsRequest": {"description": "The request for Firestore.BatchGetDocuments.", "id": "BatchGetDocumentsRequest", "properties": {"documents": {"description": "The names of the documents to retrieve. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`. The request will fail if any of the document is not a child resource of the given `database`. Duplicate names will be elided.", "items": {"type": "string"}, "type": "array"}, "mask": {"$ref": "DocumentMask", "description": "The fields to return. If not set, returns all fields. If a document has a field that is not present in this mask, that field will not be returned in the response."}, "newTransaction": {"$ref": "TransactionOptions", "description": "Starts a new transaction and reads the documents. Defaults to a read-only transaction. The new transaction ID will be returned as the first response in the stream."}, "readTime": {"description": "Reads documents as they were at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}, "transaction": {"description": "Reads documents in a transaction.", "format": "byte", "type": "string"}}, "type": "object"}, "BatchGetDocumentsResponse": {"description": "The streamed response for Firestore.BatchGetDocuments.", "id": "BatchGetDocumentsResponse", "properties": {"found": {"$ref": "Document", "description": "A document that was requested."}, "missing": {"description": "A document name that was requested but does not exist. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "type": "string"}, "readTime": {"description": "The time at which the document was read. This may be monotically increasing, in this case the previous documents in the result stream are guaranteed not to have changed between their read_time and this one.", "format": "google-datetime", "type": "string"}, "transaction": {"description": "The transaction that was started as part of this request. Will only be set in the first response, and only if BatchGetDocumentsRequest.new_transaction was set in the request.", "format": "byte", "type": "string"}}, "type": "object"}, "BatchWriteRequest": {"description": "The request for Firestore.BatchWrite.", "id": "BatchWriteRequest", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with this batch write.", "type": "object"}, "writes": {"description": "The writes to apply. Method does not apply writes atomically and does not guarantee ordering. Each write succeeds or fails independently. You cannot write to the same document more than once per request.", "items": {"$ref": "Write"}, "type": "array"}}, "type": "object"}, "BatchWriteResponse": {"description": "The response from Firestore.BatchWrite.", "id": "BatchWriteResponse", "properties": {"status": {"description": "The status of applying the writes. This i-th write status corresponds to the i-th write in the request.", "items": {"$ref": "Status"}, "type": "array"}, "writeResults": {"description": "The result of applying the writes. This i-th write result corresponds to the i-th write in the request.", "items": {"$ref": "WriteResult"}, "type": "array"}}, "type": "object"}, "BeginTransactionRequest": {"description": "The request for Firestore.BeginTransaction.", "id": "BeginTransactionRequest", "properties": {"options": {"$ref": "TransactionOptions", "description": "The options for the transaction. Defaults to a read-write transaction."}}, "type": "object"}, "BeginTransactionResponse": {"description": "The response for Firestore.BeginTransaction.", "id": "BeginTransactionResponse", "properties": {"transaction": {"description": "The transaction that was started.", "format": "byte", "type": "string"}}, "type": "object"}, "BitSequence": {"description": "A sequence of bits, encoded in a byte array. Each byte in the `bitmap` byte array stores 8 bits of the sequence. The only exception is the last byte, which may store 8 _or fewer_ bits. The `padding` defines the number of bits of the last byte to be ignored as \"padding\". The values of these \"padding\" bits are unspecified and must be ignored. To retrieve the first bit, bit 0, calculate: `(bitmap[0] & 0x01) != 0`. To retrieve the second bit, bit 1, calculate: `(bitmap[0] & 0x02) != 0`. To retrieve the third bit, bit 2, calculate: `(bitmap[0] & 0x04) != 0`. To retrieve the fourth bit, bit 3, calculate: `(bitmap[0] & 0x08) != 0`. To retrieve bit n, calculate: `(bitmap[n / 8] & (0x01 << (n % 8))) != 0`. The \"size\" of a `BitSequence` (the number of bits it contains) is calculated by this formula: `(bitmap.length * 8) - padding`.", "id": "BitSequence", "properties": {"bitmap": {"description": "The bytes that encode the bit sequence. May have a length of zero.", "format": "byte", "type": "string"}, "padding": {"description": "The number of bits of the last byte in `bitmap` to ignore as \"padding\". If the length of `bitmap` is zero, then this value must be `0`. Otherwise, this value must be between 0 and 7, inclusive.", "format": "int32", "type": "integer"}}, "type": "object"}, "BloomFilter": {"description": "A bloom filter (https://en.wikipedia.org/wiki/Bloom_filter). The bloom filter hashes the entries with MD5 and treats the resulting 128-bit hash as 2 distinct 64-bit hash values, interpreted as unsigned integers using 2's complement encoding. These two hash values, named `h1` and `h2`, are then used to compute the `hash_count` hash values using the formula, starting at `i=0`: h(i) = h1 + (i * h2) These resulting values are then taken modulo the number of bits in the bloom filter to get the bits of the bloom filter to test for the given entry.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"bits": {"$ref": "BitSequence", "description": "The bloom filter data."}, "hashCount": {"description": "The number of hashes used by the algorithm.", "format": "int32", "type": "integer"}}, "type": "object"}, "CollectionSelector": {"description": "A selection of a collection, such as `messages as m1`.", "id": "CollectionSelector", "properties": {"allDescendants": {"description": "When false, selects only collections that are immediate children of the `parent` specified in the containing `RunQueryRequest`. When true, selects all descendant collections.", "type": "boolean"}, "collectionId": {"description": "The collection ID. When set, selects only collections with this ID.", "type": "string"}}, "type": "object"}, "CommitRequest": {"description": "The request for Firestore.Commit.", "id": "CommitRequest", "properties": {"transaction": {"description": "If set, applies all writes in this transaction, and commits it.", "format": "byte", "type": "string"}, "writes": {"description": "The writes to apply. Always executed atomically and in order.", "items": {"$ref": "Write"}, "type": "array"}}, "type": "object"}, "CommitResponse": {"description": "The response for Firestore.Commit.", "id": "CommitResponse", "properties": {"commitTime": {"description": "The time at which the commit occurred. Any read with an equal or greater `read_time` is guaranteed to see the effects of the commit.", "format": "google-datetime", "type": "string"}, "writeResults": {"description": "The result of applying the writes. This i-th write result corresponds to the i-th write in the request.", "items": {"$ref": "WriteResult"}, "type": "array"}}, "type": "object"}, "CompositeFilter": {"description": "A filter that merges multiple other filters using the given operator.", "id": "CompositeFilter", "properties": {"filters": {"description": "The list of filters to combine. Requires: * At least one filter is present.", "items": {"$ref": "Filter"}, "type": "array"}, "op": {"description": "The operator for combining multiple filters.", "enum": ["OPERATOR_UNSPECIFIED", "AND", "OR"], "enumDescriptions": ["Unspecified. This value must not be used.", "Documents are required to satisfy all of the combined filters.", "Documents are required to satisfy at least one of the combined filters."], "type": "string"}}, "type": "object"}, "Count": {"description": "Count of documents that match the query. The `COUNT(*)` aggregation function operates on the entire document so it does not require a field reference.", "id": "Count", "properties": {"upTo": {"description": "Optional. Optional constraint on the maximum number of documents to count. This provides a way to set an upper bound on the number of documents to scan, limiting latency, and cost. Unspecified is interpreted as no bound. High-Level Example: ``` AGGREGATE COUNT_UP_TO(1000) OVER ( SELECT * FROM k ); ``` Requires: * Must be greater than zero when present.", "format": "int64", "type": "string"}}, "type": "object"}, "Cursor": {"description": "A position in a query result set.", "id": "<PERSON><PERSON><PERSON>", "properties": {"before": {"description": "If the position is just before or just after the given values, relative to the sort order defined by the query.", "type": "boolean"}, "values": {"description": "The values that represent a position, in the order they appear in the order by clause of a query. Can contain fewer values than specified in the order by clause.", "items": {"$ref": "Value"}, "type": "array"}}, "type": "object"}, "Document": {"description": "A Firestore document. Must not exceed 1 MiB - 4 bytes.", "id": "Document", "properties": {"createTime": {"description": "Output only. The time at which the document was created. This value increases monotonically when a document is deleted then recreated. It can also be compared to values from other documents and the `read_time` of a query.", "format": "google-datetime", "type": "string"}, "fields": {"additionalProperties": {"$ref": "Value"}, "description": "The document's fields. The map keys represent field names. Field names matching the regular expression `__.*__` are reserved. Reserved field names are forbidden except in certain documented contexts. The field names, represented as UTF-8, must not exceed 1,500 bytes and cannot be empty. Field paths may be used in other contexts to refer to structured fields defined here. For `map_value`, the field path is represented by a dot-delimited (`.`) string of segments. Each segment is either a simple field name (defined below) or a quoted field name. For example, the structured field `\"foo\" : { map_value: { \"x&y\" : { string_value: \"hello\" }}}` would be represented by the field path `` foo.`x&y` ``. A simple field name contains only characters `a` to `z`, `A` to `Z`, `0` to `9`, or `_`, and must not start with `0` to `9`. For example, `foo_bar_17`. A quoted field name starts and ends with `` ` `` and may contain any character. Some characters, including `` ` ``, must be escaped using a `\\`. For example, `` `x&y` `` represents `x&y` and `` `bak\\`tik` `` represents `` bak`tik ``.", "type": "object"}, "name": {"description": "The resource name of the document, for example `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "type": "string"}, "updateTime": {"description": "Output only. The time at which the document was last changed. This value is initially set to the `create_time` then increases monotonically with each change to the document. It can also be compared to values from other documents and the `read_time` of a query.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "DocumentChange": {"description": "A Document has changed. May be the result of multiple writes, including deletes, that ultimately resulted in a new value for the Document. Multiple DocumentChange messages may be returned for the same logical change, if multiple targets are affected.", "id": "DocumentChange", "properties": {"document": {"$ref": "Document", "description": "The new state of the Document. If `mask` is set, contains only fields that were updated or added."}, "removedTargetIds": {"description": "A set of target IDs for targets that no longer match this document.", "items": {"format": "int32", "type": "integer"}, "type": "array"}, "targetIds": {"description": "A set of target IDs of targets that match this document.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "DocumentDelete": {"description": "A Document has been deleted. May be the result of multiple writes, including updates, the last of which deleted the Document. Multiple DocumentDelete messages may be returned for the same logical delete, if multiple targets are affected.", "id": "DocumentDelete", "properties": {"document": {"description": "The resource name of the Document that was deleted.", "type": "string"}, "readTime": {"description": "The read timestamp at which the delete was observed. Greater or equal to the `commit_time` of the delete.", "format": "google-datetime", "type": "string"}, "removedTargetIds": {"description": "A set of target IDs for targets that previously matched this entity.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "DocumentMask": {"description": "A set of field paths on a document. Used to restrict a get or update operation on a document to a subset of its fields. This is different from standard field masks, as this is always scoped to a Document, and takes in account the dynamic nature of Value.", "id": "DocumentMask", "properties": {"fieldPaths": {"description": "The list of field paths in the mask. See Document.fields for a field path syntax reference.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "DocumentRemove": {"description": "A Document has been removed from the view of the targets. Sent if the document is no longer relevant to a target and is out of view. Can be sent instead of a DocumentDelete or a DocumentChange if the server can not send the new value of the document. Multiple DocumentRemove messages may be returned for the same logical write or delete, if multiple targets are affected.", "id": "DocumentRemove", "properties": {"document": {"description": "The resource name of the Document that has gone out of view.", "type": "string"}, "readTime": {"description": "The read timestamp at which the remove was observed. Greater or equal to the `commit_time` of the change/delete/remove.", "format": "google-datetime", "type": "string"}, "removedTargetIds": {"description": "A set of target IDs for targets that previously matched this document.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "DocumentTransform": {"description": "A transformation of a document.", "id": "DocumentTransform", "properties": {"document": {"description": "The name of the document to transform.", "type": "string"}, "fieldTransforms": {"description": "The list of transformations to apply to the fields of the document, in order. This must not be empty.", "items": {"$ref": "FieldTransform"}, "type": "array"}}, "type": "object"}, "DocumentsTarget": {"description": "A target specified by a set of documents names.", "id": "DocumentsTarget", "properties": {"documents": {"description": "The names of the documents to retrieve. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`. The request will fail if any of the document is not a child resource of the given `database`. Duplicate names will be elided.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ExecutionStats": {"description": "Execution statistics for the query.", "id": "ExecutionStats", "properties": {"debugStats": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "description": "Debugging statistics from the execution of the query. Note that the debugging stats are subject to change as Firestore evolves. It could include: { \"indexes_entries_scanned\": \"1000\", \"documents_scanned\": \"20\", \"billing_details\" : { \"documents_billable\": \"20\", \"index_entries_billable\": \"1000\", \"min_query_cost\": \"0\" } }", "type": "object"}, "executionDuration": {"description": "Total time to execute the query in the backend.", "format": "google-duration", "type": "string"}, "readOperations": {"description": "Total billable read operations.", "format": "int64", "type": "string"}, "resultsReturned": {"description": "Total number of results returned, including documents, projections, aggregation results, keys.", "format": "int64", "type": "string"}}, "type": "object"}, "ExistenceFilter": {"description": "A digest of all the documents that match a given target.", "id": "ExistenceFilter", "properties": {"count": {"description": "The total count of documents that match target_id. If different from the count of documents in the client that match, the client must manually determine which documents no longer match the target. The client can use the `unchanged_names` bloom filter to assist with this determination by testing ALL the document names against the filter; if the document name is NOT in the filter, it means the document no longer matches the target.", "format": "int32", "type": "integer"}, "targetId": {"description": "The target ID to which this filter applies.", "format": "int32", "type": "integer"}, "unchangedNames": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "A bloom filter that, despite its name, contains the UTF-8 byte encodings of the resource names of ALL the documents that match target_id, in the form `projects/{project_id}/databases/{database_id}/documents/{document_path}`. This bloom filter may be omitted at the server's discretion, such as if it is deemed that the client will not make use of it or if it is too computationally expensive to calculate or transmit. Clients must gracefully handle this field being absent by falling back to the logic used before this field existed; that is, re-add the target without a resume token to figure out which documents in the client's cache are out of sync."}}, "type": "object"}, "ExplainMetrics": {"description": "Explain metrics for the query.", "id": "ExplainMetrics", "properties": {"executionStats": {"$ref": "ExecutionStats", "description": "Aggregated stats from the execution of the query. Only present when ExplainOptions.analyze is set to true."}, "planSummary": {"$ref": "PlanSummary", "description": "Planning phase information for the query."}}, "type": "object"}, "ExplainOptions": {"description": "Explain options for the query.", "id": "ExplainOptions", "properties": {"analyze": {"description": "Optional. Whether to execute this query. When false (the default), the query will be planned, returning only metrics from the planning stages. When true, the query will be planned and executed, returning the full query results along with both planning and execution stage metrics.", "type": "boolean"}}, "type": "object"}, "FieldFilter": {"description": "A filter on a specific field.", "id": "<PERSON><PERSON><PERSON><PERSON>", "properties": {"field": {"$ref": "FieldReference", "description": "The field to filter by."}, "op": {"description": "The operator to filter by.", "enum": ["OPERATOR_UNSPECIFIED", "LESS_THAN", "LESS_THAN_OR_EQUAL", "GREATER_THAN", "GREATER_THAN_OR_EQUAL", "EQUAL", "NOT_EQUAL", "ARRAY_CONTAINS", "IN", "ARRAY_CONTAINS_ANY", "NOT_IN"], "enumDescriptions": ["Unspecified. This value must not be used.", "The given `field` is less than the given `value`. Requires: * That `field` come first in `order_by`.", "The given `field` is less than or equal to the given `value`. Requires: * That `field` come first in `order_by`.", "The given `field` is greater than the given `value`. Requires: * That `field` come first in `order_by`.", "The given `field` is greater than or equal to the given `value`. Requires: * That `field` come first in `order_by`.", "The given `field` is equal to the given `value`.", "The given `field` is not equal to the given `value`. Requires: * No other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the `order_by`.", "The given `field` is an array that contains the given `value`.", "The given `field` is equal to at least one value in the given array. Requires: * That `value` is a non-empty `ArrayValue`, subject to disjunction limits. * No `NOT_IN` filters in the same query.", "The given `field` is an array that contains any of the values in the given array. Requires: * That `value` is a non-empty `ArrayValue`, subject to disjunction limits. * No other `ARRAY_CONTAINS_ANY` filters within the same disjunction. * No `NOT_IN` filters in the same query.", "The value of the `field` is not in the given array. Requires: * That `value` is a non-empty `ArrayValue` with at most 10 values. * No other `OR`, `IN`, `ARRAY_CONTAINS_ANY`, `NOT_IN`, `NOT_EQUAL`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the `order_by`."], "type": "string"}, "value": {"$ref": "Value", "description": "The value to compare to."}}, "type": "object"}, "FieldReference": {"description": "A reference to a field in a document, ex: `stats.operations`.", "id": "FieldReference", "properties": {"fieldPath": {"description": "A reference to a field in a document. Requires: * MUST be a dot-delimited (`.`) string of segments, where each segment conforms to document field name limitations.", "type": "string"}}, "type": "object"}, "FieldTransform": {"description": "A transformation of a field of the document.", "id": "FieldTransform", "properties": {"appendMissingElements": {"$ref": "ArrayValue", "description": "Append the given elements in order if they are not already present in the current field value. If the field is not an array, or if the field does not yet exist, it is first set to the empty array. Equivalent numbers of different types (e.g. 3L and 3.0) are considered equal when checking if a value is missing. NaN is equal to NaN, and Null is equal to <PERSON>ull. If the input contains multiple equivalent values, only the first will be considered. The corresponding transform_result will be the null value."}, "fieldPath": {"description": "The path of the field. See Document.fields for the field path syntax reference.", "type": "string"}, "increment": {"$ref": "Value", "description": "Adds the given value to the field's current value. This must be an integer or a double value. If the field is not an integer or double, or if the field does not yet exist, the transformation will set the field to the given value. If either of the given value or the current field value are doubles, both values will be interpreted as doubles. Double arithmetic and representation of double values follow IEEE 754 semantics. If there is positive/negative integer overflow, the field is resolved to the largest magnitude positive/negative integer."}, "maximum": {"$ref": "Value", "description": "Sets the field to the maximum of its current value and the given value. This must be an integer or a double value. If the field is not an integer or double, or if the field does not yet exist, the transformation will set the field to the given value. If a maximum operation is applied where the field and the input value are of mixed types (that is - one is an integer and one is a double) the field takes on the type of the larger operand. If the operands are equivalent (e.g. 3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero. The maximum of a zero stored value and zero input value is always the stored value. The maximum of any numeric value x and NaN is NaN."}, "minimum": {"$ref": "Value", "description": "Sets the field to the minimum of its current value and the given value. This must be an integer or a double value. If the field is not an integer or double, or if the field does not yet exist, the transformation will set the field to the input value. If a minimum operation is applied where the field and the input value are of mixed types (that is - one is an integer and one is a double) the field takes on the type of the smaller operand. If the operands are equivalent (e.g. 3 and 3.0), the field does not change. 0, 0.0, and -0.0 are all zero. The minimum of a zero stored value and zero input value is always the stored value. The minimum of any numeric value x and NaN is NaN."}, "removeAllFromArray": {"$ref": "ArrayValue", "description": "Remove all of the given elements from the array in the field. If the field is not an array, or if the field does not yet exist, it is set to the empty array. Equivalent numbers of the different types (e.g. 3L and 3.0) are considered equal when deciding whether an element should be removed. NaN is equal to NaN, and Null is equal to <PERSON>ull. This will remove all equivalent values if there are duplicates. The corresponding transform_result will be the null value."}, "setToServerValue": {"description": "Sets the field to the given server value.", "enum": ["SERVER_VALUE_UNSPECIFIED", "REQUEST_TIME"], "enumDescriptions": ["Unspecified. This value must not be used.", "The time at which the server processed the request, with millisecond precision. If used on multiple fields (same or different documents) in a transaction, all the fields will get the same server timestamp."], "type": "string"}}, "type": "object"}, "Filter": {"description": "A filter.", "id": "Filter", "properties": {"compositeFilter": {"$ref": "CompositeFilter", "description": "A composite filter."}, "fieldFilter": {"$ref": "<PERSON><PERSON><PERSON><PERSON>", "description": "A filter on a document field."}, "unaryFilter": {"$ref": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "A filter that takes exactly one argument."}}, "type": "object"}, "FindNearest": {"description": "Nearest Neighbors search config. The ordering provided by FindNearest supersedes the order_by stage. If multiple documents have the same vector distance, the returned document order is not guaranteed to be stable between queries.", "id": "FindNearest", "properties": {"distanceMeasure": {"description": "Required. The distance measure to use, required.", "enum": ["DISTANCE_MEASURE_UNSPECIFIED", "EUCLIDEAN", "COSINE", "DOT_PRODUCT"], "enumDescriptions": ["Should not be set.", "Measures the EUCLIDEAN distance between the vectors. See [Euclidean](https://en.wikipedia.org/wiki/Euclidean_distance) to learn more. The resulting distance decreases the more similar two vectors are.", "COSINE distance compares vectors based on the angle between them, which allows you to measure similarity that isn't based on the vectors magnitude. We recommend using DOT_PRODUCT with unit normalized vectors instead of COSINE distance, which is mathematically equivalent with better performance. See [Cosine Similarity](https://en.wikipedia.org/wiki/Cosine_similarity) to learn more about COSINE similarity and COSINE distance. The resulting COSINE distance decreases the more similar two vectors are.", "Similar to cosine but is affected by the magnitude of the vectors. See [Dot Product](https://en.wikipedia.org/wiki/Dot_product) to learn more. The resulting distance increases the more similar two vectors are."], "type": "string"}, "distanceResultField": {"description": "Optional. Optional name of the field to output the result of the vector distance calculation. Must conform to document field name limitations.", "type": "string"}, "distanceThreshold": {"description": "Optional. Option to specify a threshold for which no less similar documents will be returned. The behavior of the specified `distance_measure` will affect the meaning of the distance threshold. Since DOT_PRODUCT distances increase when the vectors are more similar, the comparison is inverted. * For EUCLIDEAN, COSINE: `WHERE distance <= distance_threshold` * For DOT_PRODUCT: `WHERE distance >= distance_threshold`", "format": "double", "type": "number"}, "limit": {"description": "Required. The number of nearest neighbors to return. Must be a positive integer of no more than 1000.", "format": "int32", "type": "integer"}, "queryVector": {"$ref": "Value", "description": "Required. The query vector that we are searching on. Must be a vector of no more than 2048 dimensions."}, "vectorField": {"$ref": "FieldReference", "description": "Required. An indexed vector field to search upon. Only documents which contain vectors whose dimensionality match the query_vector can be returned."}}, "type": "object"}, "GoogleFirestoreAdminV1BulkDeleteDocumentsMetadata": {"description": "Metadata for google.longrunning.Operation results from FirestoreAdmin.BulkDeleteDocuments.", "id": "GoogleFirestoreAdminV1BulkDeleteDocumentsMetadata", "properties": {"collectionIds": {"description": "The IDs of the collection groups that are being deleted.", "items": {"type": "string"}, "type": "array"}, "endTime": {"description": "The time this operation completed. Will be unset if operation still in progress.", "format": "google-datetime", "type": "string"}, "namespaceIds": {"description": "Which namespace IDs are being deleted.", "items": {"type": "string"}, "type": "array"}, "operationState": {"description": "The state of the operation.", "enum": ["OPERATION_STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called google.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called google.longrunning.Operations.CancelOperation."], "type": "string"}, "progressBytes": {"$ref": "GoogleFirestoreAdminV1Progress", "description": "The progress, in bytes, of this operation."}, "progressDocuments": {"$ref": "GoogleFirestoreAdminV1Progress", "description": "The progress, in documents, of this operation."}, "snapshotTime": {"description": "The timestamp that corresponds to the version of the database that is being read to get the list of documents to delete. This time can also be used as the timestamp of PITR in case of disaster recovery (subject to PITR window limit).", "format": "google-datetime", "type": "string"}, "startTime": {"description": "The time this operation started.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1CloneDatabaseMetadata": {"description": "Metadata for the long-running operation from the CloneDatabase request.", "id": "GoogleFirestoreAdminV1CloneDatabaseMetadata", "properties": {"database": {"description": "The name of the database being cloned to.", "type": "string"}, "endTime": {"description": "The time the clone finished, unset for ongoing clones.", "format": "google-datetime", "type": "string"}, "operationState": {"description": "The operation state of the clone.", "enum": ["OPERATION_STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called google.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called google.longrunning.Operations.CancelOperation."], "type": "string"}, "pitrSnapshot": {"$ref": "GoogleFirestoreAdminV1PitrSnapshot", "description": "The snapshot from which this database was cloned."}, "progressPercentage": {"$ref": "GoogleFirestoreAdminV1Progress", "description": "How far along the clone is as an estimated percentage of remaining time."}, "startTime": {"description": "The time the clone was started.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1CreateDatabaseMetadata": {"description": "Metadata related to the create database operation.", "id": "GoogleFirestoreAdminV1CreateDatabaseMetadata", "properties": {}, "type": "object"}, "GoogleFirestoreAdminV1DeleteDatabaseMetadata": {"description": "Metadata related to the delete database operation.", "id": "GoogleFirestoreAdminV1DeleteDatabaseMetadata", "properties": {}, "type": "object"}, "GoogleFirestoreAdminV1PitrSnapshot": {"description": "A consistent snapshot of a database at a specific point in time. A PITR (Point-in-time recovery) snapshot with previous versions of a database's data is available for every minute up to the associated database's data retention period. If the PITR feature is enabled, the retention period is 7 days; otherwise, it is one hour.", "id": "GoogleFirestoreAdminV1PitrSnapshot", "properties": {"database": {"description": "Required. The name of the database that this was a snapshot of. Format: `projects/{project}/databases/{database}`.", "type": "string"}, "databaseUid": {"description": "Output only. Public UUID of the database the snapshot was associated with.", "format": "byte", "readOnly": true, "type": "string"}, "snapshotTime": {"description": "Required. Snapshot time of the database.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1Progress": {"description": "Describes the progress of the operation. Unit of work is generic and must be interpreted based on where Progress is used.", "id": "GoogleFirestoreAdminV1Progress", "properties": {"completedWork": {"description": "The amount of work completed.", "format": "int64", "type": "string"}, "estimatedWork": {"description": "The amount of work estimated.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1RestoreDatabaseMetadata": {"description": "Metadata for the long-running operation from the RestoreDatabase request.", "id": "GoogleFirestoreAdminV1RestoreDatabaseMetadata", "properties": {"backup": {"description": "The name of the backup restoring from.", "type": "string"}, "database": {"description": "The name of the database being restored to.", "type": "string"}, "endTime": {"description": "The time the restore finished, unset for ongoing restores.", "format": "google-datetime", "type": "string"}, "operationState": {"description": "The operation state of the restore.", "enum": ["OPERATION_STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called google.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called google.longrunning.Operations.CancelOperation."], "type": "string"}, "progressPercentage": {"$ref": "GoogleFirestoreAdminV1Progress", "description": "How far along the restore is as an estimated percentage of remaining time."}, "startTime": {"description": "The time the restore was started.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1UpdateDatabaseMetadata": {"description": "Metadata related to the update database operation.", "id": "GoogleFirestoreAdminV1UpdateDatabaseMetadata", "properties": {}, "type": "object"}, "GoogleFirestoreAdminV1beta1ExportDocumentsMetadata": {"description": "Metadata for ExportDocuments operations.", "id": "GoogleFirestoreAdminV1beta1ExportDocumentsMetadata", "properties": {"collectionIds": {"description": "Which collection ids are being exported.", "items": {"type": "string"}, "type": "array"}, "endTime": {"description": "The time the operation ended, either successfully or otherwise. Unset if the operation is still active.", "format": "google-datetime", "type": "string"}, "operationState": {"description": "The state of the export operation.", "enum": ["STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called google.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called google.longrunning.Operations.CancelOperation."], "type": "string"}, "outputUriPrefix": {"description": "Where the entities are being exported to.", "type": "string"}, "progressBytes": {"$ref": "GoogleFirestoreAdminV1beta1Progress", "description": "An estimate of the number of bytes processed."}, "progressDocuments": {"$ref": "GoogleFirestoreAdminV1beta1Progress", "description": "An estimate of the number of documents processed."}, "startTime": {"description": "The time that work began on the operation.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1ExportDocumentsRequest": {"description": "The request for FirestoreAdmin.ExportDocuments.", "id": "GoogleFirestoreAdminV1beta1ExportDocumentsRequest", "properties": {"collectionIds": {"description": "Which collection ids to export. Unspecified means all collections.", "items": {"type": "string"}, "type": "array"}, "outputUriPrefix": {"description": "The output URI. Currently only supports Google Cloud Storage URIs of the form: `gs://BUCKET_NAME[/NAMESPACE_PATH]`, where `BUCKET_NAME` is the name of the Google Cloud Storage bucket and `NAMESPACE_PATH` is an optional Google Cloud Storage namespace path. When choosing a name, be sure to consider Google Cloud Storage naming guidelines: https://cloud.google.com/storage/docs/naming. If the URI is a bucket (without a namespace path), a prefix will be generated based on the start time.", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1ExportDocumentsResponse": {"description": "Returned in the google.longrunning.Operation response field.", "id": "GoogleFirestoreAdminV1beta1ExportDocumentsResponse", "properties": {"outputUriPrefix": {"description": "Location of the output files. This can be used to begin an import into Cloud Firestore (this project or another project) after the operation completes successfully.", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1ImportDocumentsMetadata": {"description": "Metadata for ImportDocuments operations.", "id": "GoogleFirestoreAdminV1beta1ImportDocumentsMetadata", "properties": {"collectionIds": {"description": "Which collection ids are being imported.", "items": {"type": "string"}, "type": "array"}, "endTime": {"description": "The time the operation ended, either successfully or otherwise. Unset if the operation is still active.", "format": "google-datetime", "type": "string"}, "inputUriPrefix": {"description": "The location of the documents being imported.", "type": "string"}, "operationState": {"description": "The state of the import operation.", "enum": ["STATE_UNSPECIFIED", "INITIALIZING", "PROCESSING", "CANCELLING", "FINALIZING", "SUCCESSFUL", "FAILED", "CANCELLED"], "enumDescriptions": ["Unspecified.", "Request is being prepared for processing.", "Request is actively being processed.", "Request is in the process of being cancelled after user called google.longrunning.Operations.CancelOperation on the operation.", "Request has been processed and is in its finalization stage.", "Request has completed successfully.", "Request has finished being processed, but encountered an error.", "Request has finished being cancelled after user called google.longrunning.Operations.CancelOperation."], "type": "string"}, "progressBytes": {"$ref": "GoogleFirestoreAdminV1beta1Progress", "description": "An estimate of the number of bytes processed."}, "progressDocuments": {"$ref": "GoogleFirestoreAdminV1beta1Progress", "description": "An estimate of the number of documents processed."}, "startTime": {"description": "The time that work began on the operation.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1ImportDocumentsRequest": {"description": "The request for FirestoreAdmin.ImportDocuments.", "id": "GoogleFirestoreAdminV1beta1ImportDocumentsRequest", "properties": {"collectionIds": {"description": "Which collection ids to import. Unspecified means all collections included in the import.", "items": {"type": "string"}, "type": "array"}, "inputUriPrefix": {"description": "Location of the exported files. This must match the output_uri_prefix of an ExportDocumentsResponse from an export that has completed successfully. See: google.firestore.admin.v1beta1.ExportDocumentsResponse.output_uri_prefix.", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1Index": {"description": "An index definition.", "id": "GoogleFirestoreAdminV1beta1Index", "properties": {"collectionId": {"description": "The collection ID to which this index applies. Required.", "type": "string"}, "fields": {"description": "The fields to index.", "items": {"$ref": "GoogleFirestoreAdminV1beta1IndexField"}, "type": "array"}, "name": {"description": "The resource name of the index. Output only.", "type": "string"}, "state": {"description": "The state of the index. Output only.", "enum": ["STATE_UNSPECIFIED", "CREATING", "READY", "ERROR"], "enumDescriptions": ["The state is unspecified.", "The index is being created. There is an active long-running operation for the index. The index is updated when writing a document. Some index data may exist.", "The index is ready to be used. The index is updated when writing a document. The index is fully populated from all stored documents it applies to.", "The index was being created, but something went wrong. There is no active long-running operation for the index, and the most recently finished long-running operation failed. The index is not updated when writing a document. Some index data may exist."], "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1IndexField": {"description": "A field of an index.", "id": "GoogleFirestoreAdminV1beta1IndexField", "properties": {"fieldPath": {"description": "The path of the field. Must match the field path specification described by google.firestore.v1beta1.Document.fields. Special field path `__name__` may be used by itself or at the end of a path. `__type__` may be used only at the end of path.", "type": "string"}, "mode": {"description": "The field's mode.", "enum": ["MODE_UNSPECIFIED", "ASCENDING", "DESCENDING", "ARRAY_CONTAINS"], "enumDescriptions": ["The mode is unspecified.", "The field's values are indexed so as to support sequencing in ascending order and also query by <, >, <=, >=, and =.", "The field's values are indexed so as to support sequencing in descending order and also query by <, >, <=, >=, and =.", "The field's array values are indexed so as to support membership using ARRAY_CONTAINS queries."], "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1IndexOperationMetadata": {"description": "Metadata for index operations. This metadata populates the metadata field of google.longrunning.Operation.", "id": "GoogleFirestoreAdminV1beta1IndexOperationMetadata", "properties": {"cancelled": {"description": "True if the [google.longrunning.Operation] was cancelled. If the cancellation is in progress, cancelled will be true but google.longrunning.Operation.done will be false.", "type": "boolean"}, "documentProgress": {"$ref": "GoogleFirestoreAdminV1beta1Progress", "description": "Progress of the existing operation, measured in number of documents."}, "endTime": {"description": "The time the operation ended, either successfully or otherwise. Unset if the operation is still active.", "format": "google-datetime", "type": "string"}, "index": {"description": "The index resource that this operation is acting on. For example: `projects/{project_id}/databases/{database_id}/indexes/{index_id}`", "type": "string"}, "operationType": {"description": "The type of index operation.", "enum": ["OPERATION_TYPE_UNSPECIFIED", "CREATING_INDEX"], "enumDescriptions": ["Unspecified. Never set by server.", "The operation is creating the index. Initiated by a `CreateIndex` call."], "type": "string"}, "startTime": {"description": "The time that work began on the operation.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1ListIndexesResponse": {"description": "The response for FirestoreAdmin.ListIndexes.", "id": "GoogleFirestoreAdminV1beta1ListIndexesResponse", "properties": {"indexes": {"description": "The indexes.", "items": {"$ref": "GoogleFirestoreAdminV1beta1Index"}, "type": "array"}, "nextPageToken": {"description": "The standard List next-page token.", "type": "string"}}, "type": "object"}, "GoogleFirestoreAdminV1beta1LocationMetadata": {"description": "The metadata message for google.cloud.location.Location.metadata.", "id": "GoogleFirestoreAdminV1beta1LocationMetadata", "properties": {}, "type": "object"}, "GoogleFirestoreAdminV1beta1Progress": {"description": "Measures the progress of a particular metric.", "id": "GoogleFirestoreAdminV1beta1Progress", "properties": {"workCompleted": {"description": "An estimate of how much work has been completed. Note that this may be greater than `work_estimated`.", "format": "int64", "type": "string"}, "workEstimated": {"description": "An estimate of how much work needs to be performed. Zero if the work estimate is unavailable. May change as work progresses.", "format": "int64", "type": "string"}}, "type": "object"}, "GoogleLongrunningOperation": {"description": "This resource represents a long-running operation that is the result of a network API call.", "id": "GoogleLongrunningOperation", "properties": {"done": {"description": "If the value is `false`, it means the operation is still in progress. If `true`, the operation is completed, and either `error` or `response` is available.", "type": "boolean"}, "error": {"$ref": "Status", "description": "The error result of the operation in case of failure or cancellation."}, "metadata": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "Service-specific metadata associated with the operation. It typically contains progress information and common metadata such as create time. Some services might not provide such metadata. Any method that returns a long-running operation should document the metadata type, if any.", "type": "object"}, "name": {"description": "The server-assigned name, which is only unique within the same service that originally returns it. If you use the default HTTP mapping, the `name` should be a resource name ending with `operations/{unique_id}`.", "type": "string"}, "response": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "description": "The normal, successful response of the operation. If the original method returns no data on success, such as `Delete`, the response is `google.protobuf.Empty`. If the original method is standard `Get`/`Create`/`Update`, the response should be the resource. For other methods, the response should have the type `XxxResponse`, where `Xxx` is the original method name. For example, if the original method name is `TakeSnapshot()`, the inferred response type is `TakeSnapshotResponse`.", "type": "object"}}, "type": "object"}, "LatLng": {"description": "An object that represents a latitude/longitude pair. This is expressed as a pair of doubles to represent degrees latitude and degrees longitude. Unless specified otherwise, this object must conform to the WGS84 standard. Values must be within normalized ranges.", "id": "LatLng", "properties": {"latitude": {"description": "The latitude in degrees. It must be in the range [-90.0, +90.0].", "format": "double", "type": "number"}, "longitude": {"description": "The longitude in degrees. It must be in the range [-180.0, +180.0].", "format": "double", "type": "number"}}, "type": "object"}, "ListCollectionIdsRequest": {"description": "The request for Firestore.ListCollectionIds.", "id": "ListCollectionIdsRequest", "properties": {"pageSize": {"description": "The maximum number of results to return.", "format": "int32", "type": "integer"}, "pageToken": {"description": "A page token. Must be a value from ListCollectionIdsResponse.", "type": "string"}, "readTime": {"description": "Reads documents as they were at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ListCollectionIdsResponse": {"description": "The response from Firestore.ListCollectionIds.", "id": "ListCollectionIdsResponse", "properties": {"collectionIds": {"description": "The collection ids.", "items": {"type": "string"}, "type": "array"}, "nextPageToken": {"description": "A page token that may be used to continue the list.", "type": "string"}}, "type": "object"}, "ListDocumentsResponse": {"description": "The response for Firestore.ListDocuments.", "id": "ListDocumentsResponse", "properties": {"documents": {"description": "The Documents found.", "items": {"$ref": "Document"}, "type": "array"}, "nextPageToken": {"description": "A token to retrieve the next page of documents. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "ListenRequest": {"description": "A request for Firestore.Listen", "id": "ListenRequest", "properties": {"addTarget": {"$ref": "Target", "description": "A target to add to this stream."}, "labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with this target change.", "type": "object"}, "removeTarget": {"description": "The ID of a target to remove from this stream.", "format": "int32", "type": "integer"}}, "type": "object"}, "ListenResponse": {"description": "The response for Firestore.Listen.", "id": "ListenResponse", "properties": {"documentChange": {"$ref": "DocumentChange", "description": "A Document has changed."}, "documentDelete": {"$ref": "DocumentDelete", "description": "A Document has been deleted."}, "documentRemove": {"$ref": "DocumentRemove", "description": "A Document has been removed from a target (because it is no longer relevant to that target)."}, "filter": {"$ref": "ExistenceFilter", "description": "A filter to apply to the set of documents previously returned for the given target. Returned when documents may have been removed from the given target, but the exact documents are unknown."}, "targetChange": {"$ref": "TargetChange", "description": "Targets have changed."}}, "type": "object"}, "MapValue": {"description": "A map value.", "id": "MapValue", "properties": {"fields": {"additionalProperties": {"$ref": "Value"}, "description": "The map's fields. The map keys represent field names. Field names matching the regular expression `__.*__` are reserved. Reserved field names are forbidden except in certain documented contexts. The map keys, represented as UTF-8, must not exceed 1,500 bytes and cannot be empty.", "type": "object"}}, "type": "object"}, "Order": {"description": "An order on a field.", "id": "Order", "properties": {"direction": {"description": "The direction to order by. Defaults to `ASCENDING`.", "enum": ["DIRECTION_UNSPECIFIED", "ASCENDING", "DESCENDING"], "enumDescriptions": ["Unspecified.", "Ascending.", "Descending."], "type": "string"}, "field": {"$ref": "FieldReference", "description": "The field to order by."}}, "type": "object"}, "PartitionQueryRequest": {"description": "The request for Firestore.PartitionQuery.", "id": "PartitionQueryRequest", "properties": {"pageSize": {"description": "The maximum number of partitions to return in this call, subject to `partition_count`. For example, if `partition_count` = 10 and `page_size` = 8, the first call to PartitionQuery will return up to 8 partitions and a `next_page_token` if more results exist. A second call to PartitionQuery will return up to 2 partitions, to complete the total of 10 specified in `partition_count`.", "format": "int32", "type": "integer"}, "pageToken": {"description": "The `next_page_token` value returned from a previous call to PartitionQuery that may be used to get an additional set of results. There are no ordering guarantees between sets of results. Thus, using multiple sets of results will require merging the different result sets. For example, two subsequent calls using a page_token may return: * cursor B, cursor M, cursor Q * cursor A, cursor U, cursor W To obtain a complete result set ordered with respect to the results of the query supplied to PartitionQuery, the results sets should be merged: cursor A, cursor B, cursor M, cursor Q, cursor U, cursor W", "type": "string"}, "partitionCount": {"description": "The desired maximum number of partition points. The partitions may be returned across multiple pages of results. The number must be positive. The actual number of partitions returned may be fewer. For example, this may be set to one fewer than the number of parallel queries to be run, or in running a data pipeline job, one fewer than the number of workers or compute instances available.", "format": "int64", "type": "string"}, "readTime": {"description": "Reads documents as they were at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}, "structuredQuery": {"$ref": "StructuredQuery", "description": "A structured query. Query must specify collection with all descendants and be ordered by name ascending. Other filters, order bys, limits, offsets, and start/end cursors are not supported."}}, "type": "object"}, "PartitionQueryResponse": {"description": "The response for Firestore.PartitionQuery.", "id": "PartitionQueryResponse", "properties": {"nextPageToken": {"description": "A page token that may be used to request an additional set of results, up to the number specified by `partition_count` in the PartitionQuery request. If blank, there are no more results.", "type": "string"}, "partitions": {"description": "Partition results. Each partition is a split point that can be used by RunQuery as a starting or end point for the query results. The RunQuery requests must be made with the same query supplied to this PartitionQuery request. The partition cursors will be ordered according to same ordering as the results of the query supplied to PartitionQuery. For example, if a PartitionQuery request returns partition cursors A and B, running the following three queries will return the entire result set of the original query: * query, end_at A * query, start_at A, end_at B * query, start_at B An empty result may indicate that the query has too few results to be partitioned, or that the query is not yet supported for partitioning.", "items": {"$ref": "<PERSON><PERSON><PERSON>"}, "type": "array"}}, "type": "object"}, "PlanSummary": {"description": "Planning phase information for the query.", "id": "PlanSummary", "properties": {"indexesUsed": {"description": "The indexes selected for the query. For example: [ {\"query_scope\": \"Collection\", \"properties\": \"(foo ASC, __name__ ASC)\"}, {\"query_scope\": \"Collection\", \"properties\": \"(bar ASC, __name__ ASC)\"} ]", "items": {"additionalProperties": {"description": "Properties of the object.", "type": "any"}, "type": "object"}, "type": "array"}}, "type": "object"}, "Precondition": {"description": "A precondition on a document, used for conditional operations.", "id": "Precondition", "properties": {"exists": {"description": "When set to `true`, the target document must exist. When set to `false`, the target document must not exist.", "type": "boolean"}, "updateTime": {"description": "When set, the target document must exist and have been last updated at that time. Timestamp must be microsecond aligned.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Projection": {"description": "The projection of document's fields to return.", "id": "Projection", "properties": {"fields": {"description": "The fields to return. If empty, all fields are returned. To only return the name of the document, use `['__name__']`.", "items": {"$ref": "FieldReference"}, "type": "array"}}, "type": "object"}, "QueryTarget": {"description": "A target specified by a query.", "id": "Query<PERSON><PERSON><PERSON>", "properties": {"parent": {"description": "The parent resource name. In the format: `projects/{project_id}/databases/{database_id}/documents` or `projects/{project_id}/databases/{database_id}/documents/{document_path}`. For example: `projects/my-project/databases/my-database/documents` or `projects/my-project/databases/my-database/documents/chatrooms/my-chatroom`", "type": "string"}, "structuredQuery": {"$ref": "StructuredQuery", "description": "A structured query."}}, "type": "object"}, "ReadOnly": {"description": "Options for a transaction that can only be used to read documents.", "id": "Read<PERSON>nly", "properties": {"readTime": {"description": "Reads documents at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "ReadWrite": {"description": "Options for a transaction that can be used to read and write documents. Firestore does not allow 3rd party auth requests to create read-write. transactions.", "id": "ReadWrite", "properties": {"retryTransaction": {"description": "An optional transaction to retry.", "format": "byte", "type": "string"}}, "type": "object"}, "RollbackRequest": {"description": "The request for Firestore.Rollback.", "id": "RollbackRequest", "properties": {"transaction": {"description": "Required. The transaction to roll back.", "format": "byte", "type": "string"}}, "type": "object"}, "RunAggregationQueryRequest": {"description": "The request for Firestore.RunAggregationQuery.", "id": "RunAggregationQueryRequest", "properties": {"explainOptions": {"$ref": "ExplainOptions", "description": "Optional. Explain options for the query. If set, additional query statistics will be returned. If not, only query results will be returned."}, "newTransaction": {"$ref": "TransactionOptions", "description": "Starts a new transaction as part of the query, defaulting to read-only. The new transaction ID will be returned as the first response in the stream."}, "readTime": {"description": "Executes the query at the given timestamp. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}, "structuredAggregationQuery": {"$ref": "StructuredAggregationQuery", "description": "An aggregation query."}, "transaction": {"description": "Run the aggregation within an already active transaction. The value here is the opaque transaction ID to execute the query in.", "format": "byte", "type": "string"}}, "type": "object"}, "RunAggregationQueryResponse": {"description": "The response for Firestore.RunAggregationQuery.", "id": "RunAggregationQueryResponse", "properties": {"explainMetrics": {"$ref": "ExplainMetrics", "description": "Query explain metrics. This is only present when the RunAggregationQueryRequest.explain_options is provided, and it is sent only once with the last response in the stream."}, "readTime": {"description": "The time at which the aggregate result was computed. This is always monotonically increasing; in this case, the previous AggregationResult in the result stream are guaranteed not to have changed between their `read_time` and this one. If the query returns no results, a response with `read_time` and no `result` will be sent, and this represents the time at which the query was run.", "format": "google-datetime", "type": "string"}, "result": {"$ref": "AggregationResult", "description": "A single aggregation result. Not present when reporting partial progress."}, "transaction": {"description": "The transaction that was started as part of this request. Only present on the first response when the request requested to start a new transaction.", "format": "byte", "type": "string"}}, "type": "object"}, "RunQueryRequest": {"description": "The request for Firestore.RunQuery.", "id": "RunQueryRequest", "properties": {"explainOptions": {"$ref": "ExplainOptions", "description": "Optional. Explain options for the query. If set, additional query statistics will be returned. If not, only query results will be returned."}, "newTransaction": {"$ref": "TransactionOptions", "description": "Starts a new transaction and reads the documents. Defaults to a read-only transaction. The new transaction ID will be returned as the first response in the stream."}, "readTime": {"description": "Reads documents as they were at the given time. This must be a microsecond precision timestamp within the past one hour, or if Point-in-Time Recovery is enabled, can additionally be a whole minute timestamp within the past 7 days.", "format": "google-datetime", "type": "string"}, "structuredQuery": {"$ref": "StructuredQuery", "description": "A structured query."}, "transaction": {"description": "Run the query within an already active transaction. The value here is the opaque transaction ID to execute the query in.", "format": "byte", "type": "string"}}, "type": "object"}, "RunQueryResponse": {"description": "The response for Firestore.RunQuery.", "id": "RunQueryResponse", "properties": {"document": {"$ref": "Document", "description": "A query result, not set when reporting partial progress."}, "done": {"description": "If present, Firestore has completely finished the request and no more documents will be returned.", "type": "boolean"}, "explainMetrics": {"$ref": "ExplainMetrics", "description": "Query explain metrics. This is only present when the RunQueryRequest.explain_options is provided, and it is sent only once with the last response in the stream."}, "readTime": {"description": "The time at which the document was read. This may be monotonically increasing; in this case, the previous documents in the result stream are guaranteed not to have changed between their `read_time` and this one. If the query returns no results, a response with `read_time` and no `document` will be sent, and this represents the time at which the query was run.", "format": "google-datetime", "type": "string"}, "skippedResults": {"description": "The number of results that have been skipped due to an offset between the last response and the current response.", "format": "int32", "type": "integer"}, "transaction": {"description": "The transaction that was started as part of this request. Can only be set in the first response, and only if RunQueryRequest.new_transaction was set in the request. If set, no other fields will be set in this response.", "format": "byte", "type": "string"}}, "type": "object"}, "Status": {"description": "The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).", "id": "Status", "properties": {"code": {"description": "The status code, which should be an enum value of google.rpc.Code.", "format": "int32", "type": "integer"}, "details": {"description": "A list of messages that carry the error details. There is a common set of message types for APIs to use.", "items": {"additionalProperties": {"description": "Properties of the object. Contains field @type with type URL.", "type": "any"}, "type": "object"}, "type": "array"}, "message": {"description": "A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the google.rpc.Status.details field, or localized by the client.", "type": "string"}}, "type": "object"}, "StructuredAggregationQuery": {"description": "Firestore query for running an aggregation over a StructuredQuery.", "id": "StructuredAggregationQuery", "properties": {"aggregations": {"description": "Optional. Series of aggregations to apply over the results of the `structured_query`. Requires: * A minimum of one and maximum of five aggregations per query.", "items": {"$ref": "Aggregation"}, "type": "array"}, "structuredQuery": {"$ref": "StructuredQuery", "description": "Nested structured query."}}, "type": "object"}, "StructuredQuery": {"description": "A Firestore query. The query stages are executed in the following order: 1. from 2. where 3. select 4. order_by + start_at + end_at 5. offset 6. limit 7. find_nearest", "id": "StructuredQuery", "properties": {"endAt": {"$ref": "<PERSON><PERSON><PERSON>", "description": "A potential prefix of a position in the result set to end the query at. This is similar to `START_AT` but with it controlling the end position rather than the start position. Requires: * The number of values cannot be greater than the number of fields specified in the `ORDER BY` clause."}, "findNearest": {"$ref": "FindNearest", "description": "Optional. A potential nearest neighbors search. Applies after all other filters and ordering. Finds the closest vector embeddings to the given query vector."}, "from": {"description": "The collections to query.", "items": {"$ref": "CollectionSelector"}, "type": "array"}, "limit": {"description": "The maximum number of results to return. Applies after all other constraints. Requires: * The value must be greater than or equal to zero if specified.", "format": "int32", "type": "integer"}, "offset": {"description": "The number of documents to skip before returning the first result. This applies after the constraints specified by the `WHERE`, `START AT`, & `END AT` but before the `LIMIT` clause. Requires: * The value must be greater than or equal to zero if specified.", "format": "int32", "type": "integer"}, "orderBy": {"description": "The order to apply to the query results. Firestore allows callers to provide a full ordering, a partial ordering, or no ordering at all. In all cases, Firestore guarantees a stable ordering through the following rules: * The `order_by` is required to reference all fields used with an inequality filter. * All fields that are required to be in the `order_by` but are not already present are appended in lexicographical ordering of the field name. * If an order on `__name__` is not specified, it is appended by default. Fields are appended with the same sort direction as the last order specified, or 'ASCENDING' if no order was specified. For example: * `ORDER BY a` becomes `ORDER BY a ASC, __name__ ASC` * `ORDER BY a DESC` becomes `ORDER BY a DESC, __name__ DESC` * `WHERE a > 1` becomes `WHERE a > 1 ORDER BY a ASC, __name__ ASC` * `WHERE __name__ > ... AND a > 1` becomes `WHERE __name__ > ... AND a > 1 ORDER BY a ASC, __name__ ASC`", "items": {"$ref": "Order"}, "type": "array"}, "select": {"$ref": "Projection", "description": "Optional sub-set of the fields to return. This acts as a DocumentMask over the documents returned from a query. When not set, assumes that the caller wants all fields returned."}, "startAt": {"$ref": "<PERSON><PERSON><PERSON>", "description": "A potential prefix of a position in the result set to start the query at. The ordering of the result set is based on the `ORDER BY` clause of the original query. ``` SELECT * FROM k WHERE a = 1 AND b > 2 ORDER BY b ASC, __name__ ASC; ``` This query's results are ordered by `(b ASC, __name__ ASC)`. Cursors can reference either the full ordering or a prefix of the location, though it cannot reference more fields than what are in the provided `ORDER BY`. Continuing off the example above, attaching the following start cursors will have varying impact: - `START BEFORE (2, /k/123)`: start the query right before `a = 1 AND b > 2 AND __name__ > /k/123`. - `START AFTER (10)`: start the query right after `a = 1 AND b > 10`. Unlike `OFFSET` which requires scanning over the first N results to skip, a start cursor allows the query to begin at a logical position. This position is not required to match an actual result, it will scan forward from this position to find the next document. Requires: * The number of values cannot be greater than the number of fields specified in the `ORDER BY` clause."}, "where": {"$ref": "Filter", "description": "The filter to apply."}}, "type": "object"}, "Sum": {"description": "Sum of the values of the requested field. * Only numeric values will be aggregated. All non-numeric values including `NULL` are skipped. * If the aggregated values contain `NaN`, returns `NaN`. Infinity math follows IEEE-754 standards. * If the aggregated value set is empty, returns 0. * Returns a 64-bit integer if all aggregated numbers are integers and the sum result does not overflow. Otherwise, the result is returned as a double. Note that even if all the aggregated values are integers, the result is returned as a double if it cannot fit within a 64-bit signed integer. When this occurs, the returned value will lose precision. * When underflow occurs, floating-point aggregation is non-deterministic. This means that running the same query repeatedly without any changes to the underlying values could produce slightly different results each time. In those cases, values should be stored as integers over floating-point numbers.", "id": "Sum", "properties": {"field": {"$ref": "FieldReference", "description": "The field to aggregate on."}}, "type": "object"}, "Target": {"description": "A specification of a set of documents to listen to.", "id": "Target", "properties": {"documents": {"$ref": "DocumentsTarget", "description": "A target specified by a set of document names."}, "expectedCount": {"description": "The number of documents that last matched the query at the resume token or read time. This value is only relevant when a `resume_type` is provided. This value being present and greater than zero signals that the client wants `ExistenceFilter.unchanged_names` to be included in the response.", "format": "int32", "type": "integer"}, "once": {"description": "If the target should be removed once it is current and consistent.", "type": "boolean"}, "query": {"$ref": "Query<PERSON><PERSON><PERSON>", "description": "A target specified by a query."}, "readTime": {"description": "Start listening after a specific `read_time`. The client must know the state of matching documents at this time.", "format": "google-datetime", "type": "string"}, "resumeToken": {"description": "A resume token from a prior TargetChange for an identical target. Using a resume token with a different target is unsupported and may fail.", "format": "byte", "type": "string"}, "targetId": {"description": "The target ID that identifies the target on the stream. Must be a positive number and non-zero. If `target_id` is 0 (or unspecified), the server will assign an ID for this target and return that in a `TargetChange::ADD` event. Once a target with `target_id=0` is added, all subsequent targets must also have `target_id=0`. If an `AddTarget` request with `target_id != 0` is sent to the server after a target with `target_id=0` is added, the server will immediately send a response with a `TargetChange::Remove` event. Note that if the client sends multiple `AddTarget` requests without an ID, the order of IDs returned in `TargetChange.target_ids` are undefined. Therefore, clients should provide a target ID instead of relying on the server to assign one. If `target_id` is non-zero, there must not be an existing active target on this stream with the same ID.", "format": "int32", "type": "integer"}}, "type": "object"}, "TargetChange": {"description": "Targets being watched have changed.", "id": "TargetChange", "properties": {"cause": {"$ref": "Status", "description": "The error that resulted in this change, if applicable."}, "readTime": {"description": "The consistent `read_time` for the given `target_ids` (omitted when the target_ids are not at a consistent snapshot). The stream is guaranteed to send a `read_time` with `target_ids` empty whenever the entire stream reaches a new consistent snapshot. ADD, CURRENT, and RESET messages are guaranteed to (eventually) result in a new consistent snapshot (while NO_CHANGE and REMOVE messages are not). For a given stream, `read_time` is guaranteed to be monotonically increasing.", "format": "google-datetime", "type": "string"}, "resumeToken": {"description": "A token that can be used to resume the stream for the given `target_ids`, or all targets if `target_ids` is empty. Not set on every target change.", "format": "byte", "type": "string"}, "targetChangeType": {"description": "The type of change that occurred.", "enum": ["NO_CHANGE", "ADD", "REMOVE", "CURRENT", "RESET"], "enumDescriptions": ["No change has occurred. Used only to send an updated `resume_token`.", "The targets have been added.", "The targets have been removed.", "The targets reflect all changes committed before the targets were added to the stream. This will be sent after or with a `read_time` that is greater than or equal to the time at which the targets were added. Listeners can wait for this change if read-after-write semantics are desired.", "The targets have been reset, and a new initial state for the targets will be returned in subsequent changes. After the initial state is complete, `CURRENT` will be returned even if the target was previously indicated to be `CURRENT`."], "type": "string"}, "targetIds": {"description": "The target IDs of targets that have changed. If empty, the change applies to all targets. The order of the target IDs is not defined.", "items": {"format": "int32", "type": "integer"}, "type": "array"}}, "type": "object"}, "TransactionOptions": {"description": "Options for creating a new transaction.", "id": "TransactionOptions", "properties": {"readOnly": {"$ref": "Read<PERSON>nly", "description": "The transaction can only be used for read operations."}, "readWrite": {"$ref": "ReadWrite", "description": "The transaction can be used for both read and write operations."}}, "type": "object"}, "UnaryFilter": {"description": "A filter with a single operand.", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "properties": {"field": {"$ref": "FieldReference", "description": "The field to which to apply the operator."}, "op": {"description": "The unary operator to apply.", "enum": ["OPERATOR_UNSPECIFIED", "IS_NAN", "IS_NULL", "IS_NOT_NAN", "IS_NOT_NULL"], "enumDescriptions": ["Unspecified. This value must not be used.", "The given `field` is equal to `NaN`.", "The given `field` is equal to `NULL`.", "The given `field` is not equal to `NaN`. Requires: * No other `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the `order_by`.", "The given `field` is not equal to `NULL`. Requires: * A single `NOT_EQUAL`, `NOT_IN`, `IS_NOT_NULL`, or `IS_NOT_NAN`. * That `field` comes first in the `order_by`."], "type": "string"}}, "type": "object"}, "Value": {"description": "A message that can hold any of the supported value types.", "id": "Value", "properties": {"arrayValue": {"$ref": "ArrayValue", "description": "An array value. Cannot directly contain another array value, though can contain a map which contains another array."}, "booleanValue": {"description": "A boolean value.", "type": "boolean"}, "bytesValue": {"description": "A bytes value. Must not exceed 1 MiB - 89 bytes. Only the first 1,500 bytes are considered by queries.", "format": "byte", "type": "string"}, "doubleValue": {"description": "A double value.", "format": "double", "type": "number"}, "geoPointValue": {"$ref": "LatLng", "description": "A geo point value representing a point on the surface of Earth."}, "integerValue": {"description": "An integer value.", "format": "int64", "type": "string"}, "mapValue": {"$ref": "MapValue", "description": "A map value."}, "nullValue": {"description": "A null value.", "enum": ["NULL_VALUE"], "enumDescriptions": ["Null value."], "type": "string"}, "referenceValue": {"description": "A reference to a document. For example: `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "type": "string"}, "stringValue": {"description": "A string value. The string, represented as UTF-8, must not exceed 1 MiB - 89 bytes. Only the first 1,500 bytes of the UTF-8 representation are considered by queries.", "type": "string"}, "timestampValue": {"description": "A timestamp value. Precise only to microseconds. When stored, any additional precision is rounded down.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "Write": {"description": "A write on a document.", "id": "Write", "properties": {"currentDocument": {"$ref": "Precondition", "description": "An optional precondition on the document. The write will fail if this is set and not met by the target document."}, "delete": {"description": "A document name to delete. In the format: `projects/{project_id}/databases/{database_id}/documents/{document_path}`.", "type": "string"}, "transform": {"$ref": "DocumentTransform", "description": "Applies a transformation to a document."}, "update": {"$ref": "Document", "description": "A document to write."}, "updateMask": {"$ref": "DocumentMask", "description": "The fields to update in this write. This field can be set only when the operation is `update`. If the mask is not set for an `update` and the document exists, any existing data will be overwritten. If the mask is set and the document on the server has fields not covered by the mask, they are left unchanged. Fields referenced in the mask, but not present in the input document, are deleted from the document on the server. The field paths in this mask must not contain a reserved field name."}, "updateTransforms": {"description": "The transforms to perform after update. This field can be set only when the operation is `update`. If present, this write is equivalent to performing `update` and `transform` to the same document atomically and in order.", "items": {"$ref": "FieldTransform"}, "type": "array"}}, "type": "object"}, "WriteRequest": {"description": "The request for Firestore.Write. The first request creates a stream, or resumes an existing one from a token. When creating a new stream, the server replies with a response containing only an ID and a token, to use in the next request. When resuming a stream, the server first streams any responses later than the given token, then a response containing only an up-to-date token, to use in the next request.", "id": "WriteRequest", "properties": {"labels": {"additionalProperties": {"type": "string"}, "description": "Labels associated with this write request.", "type": "object"}, "streamId": {"description": "The ID of the write stream to resume. This may only be set in the first message. When left empty, a new write stream will be created.", "type": "string"}, "streamToken": {"description": "A stream token that was previously sent by the server. The client should set this field to the token from the most recent WriteResponse it has received. This acknowledges that the client has received responses up to this token. After sending this token, earlier tokens may not be used anymore. The server may close the stream if there are too many unacknowledged responses. Leave this field unset when creating a new stream. To resume a stream at a specific point, set this field and the `stream_id` field. Leave this field unset when creating a new stream.", "format": "byte", "type": "string"}, "writes": {"description": "The writes to apply. Always executed atomically and in order. This must be empty on the first request. This may be empty on the last request. This must not be empty on all other requests.", "items": {"$ref": "Write"}, "type": "array"}}, "type": "object"}, "WriteResponse": {"description": "The response for Firestore.Write.", "id": "WriteResponse", "properties": {"commitTime": {"description": "The time at which the commit occurred. Any read with an equal or greater `read_time` is guaranteed to see the effects of the write.", "format": "google-datetime", "type": "string"}, "streamId": {"description": "The ID of the stream. Only set on the first message, when a new stream was created.", "type": "string"}, "streamToken": {"description": "A token that represents the position of this response in the stream. This can be used by a client to resume the stream at this point. This field is always set.", "format": "byte", "type": "string"}, "writeResults": {"description": "The result of applying the writes. This i-th write result corresponds to the i-th write in the request.", "items": {"$ref": "WriteResult"}, "type": "array"}}, "type": "object"}, "WriteResult": {"description": "The result of applying a write.", "id": "WriteResult", "properties": {"transformResults": {"description": "The results of applying each DocumentTransform.FieldTransform, in the same order.", "items": {"$ref": "Value"}, "type": "array"}, "updateTime": {"description": "The last update time of the document after applying the write. Not set after a `delete`. If the write did not actually change the document, this will be the previous update_time.", "format": "google-datetime", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Cloud Firestore API", "version": "v1beta1", "version_module": true}