{% extends 'base.html' %}
{% load widget_tweaks %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="text-light">
                    <i class="bi bi-robot"></i> AI Content Studio
                </h1>
                <div class="badge bg-primary fs-6">
                    <i class="bi bi-sparkles"></i> Powered by AI
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Input Panel -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-pencil-square"></i> Content Input
                    </h5>
                </div>
                <div class="card-body">
                    <form hx-post="{% url 'generate-ai-content' %}" 
                          hx-target="#ai-results" 
                          hx-indicator="#loading-indicator">
                        {% csrf_token %}
                        
                        <div class="mb-3">
                            <label for="image_description" class="form-label">
                                <i class="bi bi-image"></i> Image Description
                            </label>
                            <textarea class="form-control" 
                                      id="image_description" 
                                      name="image_description" 
                                      rows="3" 
                                      placeholder="Describe the image you want to post about..."></textarea>
                            <div class="form-text">Describe what's in your image or what you want to create</div>
                        </div>

                        <div class="mb-3">
                            <label for="news_context" class="form-label">
                                <i class="bi bi-newspaper"></i> News Context
                            </label>
                            <textarea class="form-control" 
                                      id="news_context" 
                                      name="news_context" 
                                      rows="3" 
                                      placeholder="Paste news article or context here..."></textarea>
                            <div class="form-text">Add relevant news or context for your post</div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="tone" class="form-label">
                                    <i class="bi bi-chat-text"></i> Tone
                                </label>
                                <select class="form-select" id="tone" name="tone">
                                    {% for value, label in tone_options %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>

                            <div class="col-md-6 mb-3">
                                <label for="industry" class="form-label">
                                    <i class="bi bi-building"></i> Industry
                                </label>
                                <select class="form-select" id="industry" name="industry">
                                    {% for value, label in industry_options %}
                                        <option value="{{ value }}">{{ label }}</option>
                                    {% endfor %}
                                </select>
                            </div>
                        </div>

                        <button type="submit" class="btn btn-primary w-100">
                            <i class="bi bi-magic"></i> Generate AI Content
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <!-- Results Panel -->
        <div class="col-lg-6 mb-4">
            <div class="card h-100">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-stars"></i> AI Generated Content
                    </h5>
                </div>
                <div class="card-body">
                    <div id="loading-indicator" class="htmx-indicator text-center py-5">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Generating content...</span>
                        </div>
                        <p class="mt-2 text-muted">AI is crafting your content...</p>
                    </div>
                    
                    <div id="ai-results">
                        <div class="text-center py-5 text-muted">
                            <i class="bi bi-lightbulb display-4"></i>
                            <p class="mt-3">Fill out the form and click "Generate AI Content" to get started!</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Features Overview -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle"></i> AI Features
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-chat-quote display-6 text-primary"></i>
                                <h6 class="mt-2">Smart Captions</h6>
                                <p class="text-muted small">AI generates engaging captions based on your content and tone preferences</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-hash display-6 text-success"></i>
                                <h6 class="mt-2">Hashtag Intelligence</h6>
                                <p class="text-muted small">Get relevant hashtags that boost your content's discoverability</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-clock display-6 text-warning"></i>
                                <h6 class="mt-2">Optimal Timing</h6>
                                <p class="text-muted small">AI suggests the best times to post for maximum engagement</p>
                            </div>
                        </div>
                        <div class="col-md-3 mb-3">
                            <div class="text-center">
                                <i class="bi bi-graph-up display-6 text-info"></i>
                                <h6 class="mt-2">Performance Prediction</h6>
                                <p class="text-muted small">Predict how well your content will perform before posting</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.htmx-indicator {
    display: none;
}

.htmx-request .htmx-indicator {
    display: block;
}

.card {
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card-header {
    background: linear-gradient(45deg, var(--accent-color), #0056b3);
    color: white;
    border: none;
}

.badge {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.display-6 {
    opacity: 0.8;
    transition: opacity 0.3s ease;
}

.display-6:hover {
    opacity: 1;
}
</style>
{% endblock %}
