INFO 2025-07-12 12:12:54,528 autoreload 3452 14044 Watching for file changes with StatReloader
ERROR 2025-07-12 12:14:15,813 setup_project 14432 13616 Project setup failed: no such column: images_search.search_count
INFO 2025-07-12 12:14:56,444 autoreload 14676 15092 Watching for file changes with StatReloader
INFO 2025-07-12 12:22:57,855 autoreload 148 16072 Watching for file changes with StatReloader
INFO 2025-07-12 12:23:23,478 autoreload 8980 16876 Watching for file changes with StatReloader
INFO 2025-07-12 12:24:53,497 autoreload 11636 16760 Watching for file changes with StatReloader
INFO 2025-07-12 12:25:29,527 autoreload 17356 14108 Watching for file changes with StatReloader
ERROR 2025-07-12 12:25:39,031 log 17356 14872 Internal Server Error: /ai-studio/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 539, in ai_content_studio
    return redirect('login')
           ^^^^^^^^
NameError: name 'redirect' is not defined
ERROR 2025-07-12 12:25:39,053 basehttp 17356 14872 "GET /ai-studio/ HTTP/1.1" 500 71572
WARNING 2025-07-12 12:25:41,208 log 17356 14872 Not Found: /favicon.ico
WARNING 2025-07-12 12:25:41,211 basehttp 17356 14872 "GET /favicon.ico HTTP/1.1" 404 5578
INFO 2025-07-12 12:25:43,449 autoreload 17356 14108 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:25:48,500 autoreload 6012 6836 Watching for file changes with StatReloader
INFO 2025-07-12 12:26:03,092 basehttp 6012 16944 - Broken pipe from ('127.0.0.1', 54628)
INFO 2025-07-12 12:26:04,422 basehttp 6012 17340 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:05,729 basehttp 6012 17340 "GET /static/css/style.css HTTP/1.1" 200 5462
INFO 2025-07-12 12:26:06,131 basehttp 6012 15812 "GET /static/images/logoo.png HTTP/1.1" 200 7079
INFO 2025-07-12 12:26:08,122 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6013
INFO 2025-07-12 12:26:14,055 basehttp 6012 15812 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-12 12:26:14,077 basehttp 6012 15812 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:16,866 basehttp 6012 15812 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:22,238 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
ERROR 2025-07-12 12:26:25,369 log 6012 15812 Internal Server Error: /images
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_result.image_alt

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 226, in get
    context = self.get_context_data(**kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 64, in get_context_data
    images = list(Result.objects.all())
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_result.image_alt
ERROR 2025-07-12 12:26:25,414 basehttp 6012 15812 "GET /images HTTP/1.1" 500 154259
INFO 2025-07-12 12:26:27,922 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
ERROR 2025-07-12 12:26:29,425 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:26:29,452 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203501
INFO 2025-07-12 12:26:32,302 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
INFO 2025-07-12 12:26:33,144 basehttp 6012 15812 "GET /ai-studio/ HTTP/1.1" 200 14914
ERROR 2025-07-12 12:26:43,107 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:26:43,154 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203505
ERROR 2025-07-12 12:27:07,218 log 6012 15812 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 575, in analytics_dashboard
    posted_count = user_posts.filter(status='posted').count()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:27:07,241 basehttp 6012 15812 "GET /analytics/ HTTP/1.1" 500 136504
ERROR 2025-07-12 12:28:22,425 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:28:22,447 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 140951
ERROR 2025-07-12 12:29:22,506 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:29:22,653 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141088
ERROR 2025-07-12 12:29:54,866 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:29:54,891 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141088
ERROR 2025-07-12 12:30:00,441 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:00,480 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203505
INFO 2025-07-12 12:30:03,299 basehttp 6012 15812 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 12:30:04,410 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:07,627 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:07,645 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141108
INFO 2025-07-12 12:30:09,757 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:11,395 log 6012 15812 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 575, in analytics_dashboard
    posted_count = user_posts.filter(status='posted').count()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:11,422 basehttp 6012 15812 "GET /analytics/ HTTP/1.1" 500 136661
INFO 2025-07-12 12:30:14,276 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:16,587 log 6012 15812 Internal Server Error: /images
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_result.image_alt

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 226, in get
    context = self.get_context_data(**kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 64, in get_context_data
    images = list(Result.objects.all())
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_result.image_alt
ERROR 2025-07-12 12:30:16,614 basehttp 6012 15812 "GET /images HTTP/1.1" 500 154259
INFO 2025-07-12 12:30:18,261 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
INFO 2025-07-12 12:32:38,372 autoreload 6012 6836 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 12:32:42,724 autoreload 9768 17028 Watching for file changes with StatReloader
INFO 2025-07-12 12:32:59,918 autoreload 9768 17028 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 12:33:04,652 autoreload 16020 5768 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:15,353 autoreload 16020 5768 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:20,505 autoreload 17328 10400 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:29,999 autoreload 17328 10400 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:34,489 autoreload 4184 16440 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:43,348 autoreload 4184 16440 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:49,719 autoreload 14744 15272 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:00,248 autoreload 14744 15272 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:06,065 autoreload 4168 10848 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:19,428 autoreload 4168 10848 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:25,463 autoreload 4340 15416 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:36,565 autoreload 4340 15416 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:42,325 autoreload 6440 17316 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:52,692 autoreload 6440 17316 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:58,557 autoreload 2356 17256 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:09,065 autoreload 2356 17256 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:14,981 autoreload 2284 7120 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:25,275 autoreload 2284 7120 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:31,270 autoreload 17208 16868 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:39,863 autoreload 17208 16868 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:46,436 autoreload 11936 9824 Watching for file changes with StatReloader
INFO 2025-07-12 12:36:55,412 autoreload 16788 16556 Watching for file changes with StatReloader
INFO 2025-07-12 12:38:22,020 autoreload 11936 9824 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:38:28,838 autoreload 12348 14836 Watching for file changes with StatReloader
INFO 2025-07-12 12:38:39,213 autoreload 12348 14836 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:38:44,205 autoreload 3624 3476 Watching for file changes with StatReloader
INFO 2025-07-12 12:40:48,083 autoreload 3624 3476 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 12:43:53,902 autoreload 16440 14460 Watching for file changes with StatReloader
INFO 2025-07-12 12:44:35,753 autoreload 4496 9520 Watching for file changes with StatReloader
INFO 2025-07-12 12:44:50,580 basehttp 4496 5036 "GET / HTTP/1.1" 200 121798
INFO 2025-07-12 12:44:51,951 basehttp 4496 5036 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 12:44:52,189 basehttp 4496 5036 "GET /static/images/logoo.png HTTP/1.1" 304 0
INFO 2025-07-12 12:44:53,069 basehttp 4496 5036 "GET /static/images/tech.jpg HTTP/1.1" 200 855
INFO 2025-07-12 12:44:58,816 basehttp 4496 5036 "GET /multi-platform/ HTTP/1.1" 200 17072
INFO 2025-07-12 12:45:16,175 basehttp 4496 5036 "GET /analytics/ HTTP/1.1" 200 17084
INFO 2025-07-12 12:45:25,784 autoreload 4496 9520 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:45:32,495 autoreload 4456 17096 Watching for file changes with StatReloader
INFO 2025-07-12 12:45:39,126 basehttp 4456 13608 "GET /analytics/ HTTP/1.1" 200 17084
INFO 2025-07-12 12:45:40,478 basehttp 4456 13608 "GET /analytics/ HTTP/1.1" 200 17084
INFO 2025-07-12 12:45:41,156 basehttp 4456 13608 "GET /ai-studio/ HTTP/1.1" 200 15413
INFO 2025-07-12 12:45:42,827 autoreload 4456 17096 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:45:47,878 autoreload 17100 15720 Watching for file changes with StatReloader
INFO 2025-07-12 12:45:53,309 basehttp 17100 3964 "GET /posts/ HTTP/1.1" 200 17287
INFO 2025-07-12 12:46:06,878 basehttp 17100 3964 "GET /images HTTP/1.1" 200 17023
ERROR 2025-07-12 12:46:15,289 log 17100 3964 Internal Server Error: /load_feed
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
TypeError: get_feed() takes 0 positional arguments but 1 was given
ERROR 2025-07-12 12:46:15,297 basehttp 17100 3964 "GET /load_feed HTTP/1.1" 500 68512
INFO 2025-07-12 12:46:18,827 basehttp 17100 3964 "GET /create_custom/ HTTP/1.1" 200 7687
INFO 2025-07-12 12:46:24,290 basehttp 17100 3964 "POST /new_feed/ HTTP/1.1" 200 8770
INFO 2025-07-12 12:46:34,502 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:34,674 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:35,787 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:36,067 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:36,157 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:36,325 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:36,565 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:37,043 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:37,344 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:38,061 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:38,242 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:38,895 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:39,751 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
INFO 2025-07-12 12:46:40,444 basehttp 17100 3964 "POST /search_images/ HTTP/1.1" 200 104
ERROR 2025-07-12 12:46:41,279 views 17100 3964 Unexpected error in search_for_images: no such column: images_search.search_count
INFO 2025-07-12 12:46:41,289 basehttp 17100 3964 "POST /search/ HTTP/1.1" 200 9403
INFO 2025-07-12 12:46:47,259 basehttp 17100 3964 "GET /ai-studio/ HTTP/1.1" 200 15413
INFO 2025-07-12 12:46:48,739 basehttp 17100 3964 "GET /analytics/ HTTP/1.1" 200 17084
ERROR 2025-07-12 12:46:53,483 log 17100 3964 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'calendar_tags'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 648, in content_calendar
    return render(request, 'calendar.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'calendar_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
dict_extras
django_htmx
i18n
l10n
log
static
tz
widget_tweaks
ERROR 2025-07-12 12:46:53,514 basehttp 17100 3964 "GET /calendar/ HTTP/1.1" **********
INFO 2025-07-12 12:46:56,670 basehttp 17100 3964 "GET /multi-platform/ HTTP/1.1" 200 17072
INFO 2025-07-12 12:47:12,250 basehttp 17100 3964 "GET /templates/ HTTP/1.1" 200 38477
INFO 2025-07-12 12:47:27,269 basehttp 17100 3964 "GET /ai-studio/ HTTP/1.1" 200 15577
INFO 2025-07-12 12:47:29,816 basehttp 17100 3964 "GET /templates/ HTTP/1.1" 200 38477
INFO 2025-07-12 12:47:33,620 autoreload 17100 15720 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:47:42,167 autoreload 15156 3692 Watching for file changes with StatReloader
INFO 2025-07-12 12:47:53,442 autoreload 15156 3692 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:47:59,836 autoreload 15404 15372 Watching for file changes with StatReloader
INFO 2025-07-12 12:49:05,833 basehttp 15404 17284 "GET /templates/ HTTP/1.1" 200 38477
INFO 2025-07-12 12:49:08,560 basehttp 15404 17284 "GET /multi-platform/ HTTP/1.1" 200 17236
ERROR 2025-07-12 12:49:15,382 log 15404 17284 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1026, in find_library
    return parser.libraries[name]
           ~~~~~~~~~~~~~~~~^^^^^^
KeyError: 'calendar_tags'

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 648, in content_calendar
    return render(request, 'calendar.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1088, in load
    lib = find_library(parser, name)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 1028, in find_library
    raise TemplateSyntaxError(
    ...<5 lines>...
    )
django.template.exceptions.TemplateSyntaxError: 'calendar_tags' is not a registered tag library. Must be one of:
admin_list
admin_modify
admin_urls
cache
dict_extras
django_htmx
i18n
l10n
log
static
tz
widget_tweaks
ERROR 2025-07-12 12:49:15,424 basehttp 15404 17284 "GET /calendar/ HTTP/1.1" **********
ERROR 2025-07-12 12:49:22,764 log 15404 17284 Internal Server Error: /bulk-upload/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 949, in bulk_upload
    return render(request, 'bulk_upload.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 703, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '{ max_files' from '{ max_files'
ERROR 2025-07-12 12:49:22,786 basehttp 15404 17284 "GET /bulk-upload/ HTTP/1.1" **********
INFO 2025-07-12 12:51:29,735 basehttp 15404 17284 "GET / HTTP/1.1" **********
INFO 2025-07-12 12:51:30,785 basehttp 15404 17284 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 12:51:32,426 basehttp 15404 17284 "GET /admin HTTP/1.1" 301 0
INFO 2025-07-12 12:51:32,770 basehttp 15404 13144 "GET /admin/ HTTP/1.1" 200 7065
INFO 2025-07-12 12:51:35,743 basehttp 15404 16708 "GET /static/admin/css/responsive.css HTTP/1.1" 200 19548
INFO 2025-07-12 12:51:35,762 basehttp 15404 16708 "GET /static/admin/js/nav_sidebar.js HTTP/1.1" 200 1399
INFO 2025-07-12 12:51:35,779 basehttp 15404 7148 "GET /static/admin/css/dashboard.css HTTP/1.1" 200 406
INFO 2025-07-12 12:51:35,817 basehttp 15404 13144 "GET /static/admin/css/base.css HTTP/1.1" 200 20565
INFO 2025-07-12 12:51:35,865 basehttp 15404 13144 "GET /static/admin/css/fonts.css HTTP/1.1" 200 443
INFO 2025-07-12 12:51:35,874 basehttp 15404 6408 "GET /static/admin/js/theme.js HTTP/1.1" 200 1943
INFO 2025-07-12 12:51:35,879 basehttp 15404 12080 "GET /static/admin/css/dark_mode.css HTTP/1.1" 200 2929
INFO 2025-07-12 12:51:35,894 basehttp 15404 1472 "GET /static/admin/css/nav_sidebar.css HTTP/1.1" 200 2391
INFO 2025-07-12 12:51:36,034 basehttp 15404 1472 "GET /static/admin/img/icon-addlink.svg HTTP/1.1" 200 334
INFO 2025-07-12 12:51:36,035 basehttp 15404 6408 "GET /static/admin/img/icon-changelink.svg HTTP/1.1" 200 383
INFO 2025-07-12 12:51:36,298 basehttp 15404 1472 "GET /static/admin/fonts/Roboto-Light-webfont.woff HTTP/1.1" 200 85692
INFO 2025-07-12 12:51:36,305 basehttp 15404 6408 "GET /static/admin/fonts/Roboto-Regular-webfont.woff HTTP/1.1" 200 85876
INFO 2025-07-12 12:51:36,314 basehttp 15404 13144 "GET /static/admin/fonts/Roboto-Bold-webfont.woff HTTP/1.1" 200 86184
ERROR 2025-07-12 12:51:44,567 log 15404 13144 Internal Server Error: /bulk-upload/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 949, in bulk_upload
    return render(request, 'bulk_upload.html', context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\shortcuts.py", line 24, in render
    content = loader.render_to_string(template_name, context, request, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 61, in render_to_string
    template = get_template(template_name, using=using)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader.py", line 15, in get_template
    return engine.get_template(template_name)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 33, in get_template
    return Template(self.engine.get_template(template_name), self)
                    ~~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 175, in get_template
    template, origin = self.find_template(template_name)
                       ~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\engine.py", line 157, in find_template
    template = loader.get_template(name, skip=skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\cached.py", line 57, in get_template
    template = super().get_template(template_name, skip)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loaders\base.py", line 28, in get_template
    return Template(
        contents,
    ...<2 lines>...
        self.engine,
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 154, in __init__
    self.nodelist = self.compile_nodelist()
                    ~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 200, in compile_nodelist
    return parser.parse()
           ~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 293, in do_extends
    nodelist = parser.parse()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 513, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 511, in parse
    compiled_result = compile_func(self, token)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 232, in do_block
    nodelist = parser.parse(("endblock",))
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 484, in parse
    raise self.error(token, e)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 482, in parse
    filter_expression = self.compile_filter(token.contents)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 600, in compile_filter
    return FilterExpression(token, self)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 703, in __init__
    raise TemplateSyntaxError(
    ...<2 lines>...
    )
django.template.exceptions.TemplateSyntaxError: Could not parse the remainder: '{ max_files' from '{ max_files'
ERROR 2025-07-12 12:51:44,593 basehttp 15404 13144 "GET /bulk-upload/ HTTP/1.1" **********
INFO 2025-07-12 12:51:47,845 basehttp 15404 13144 "GET /templates/ HTTP/1.1" 200 38648
ERROR 2025-07-12 12:51:51,010 log 15404 13144 Internal Server Error: /templates/preview/2/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 892, in template_preview
    return JsonResponse({
           ^^^^^^^^^^^^
NameError: name 'JsonResponse' is not defined. Did you mean: 'HttpResponse'?
ERROR 2025-07-12 12:51:51,031 basehttp 15404 13144 "GET /templates/preview/2/ HTTP/1.1" 500 74105
INFO 2025-07-12 12:51:53,619 basehttp 15404 13144 "GET /ai-studio/?template=true HTTP/1.1" 200 15748
INFO 2025-07-12 12:51:58,037 basehttp 15404 13144 "GET /templates/ HTTP/1.1" 200 38648
INFO 2025-07-12 12:52:05,954 basehttp 15404 13144 "GET /posts/ HTTP/1.1" 200 17622
INFO 2025-07-12 12:52:08,114 basehttp 15404 13144 "GET /analytics/ HTTP/1.1" 200 17419
