INFO 2025-07-12 12:12:54,528 autoreload 3452 14044 Watching for file changes with StatReloader
ERROR 2025-07-12 12:14:15,813 setup_project 14432 13616 Project setup failed: no such column: images_search.search_count
INFO 2025-07-12 12:14:56,444 autoreload 14676 15092 Watching for file changes with StatReloader
INFO 2025-07-12 12:22:57,855 autoreload 148 16072 Watching for file changes with StatReloader
INFO 2025-07-12 12:23:23,478 autoreload 8980 16876 Watching for file changes with StatReloader
INFO 2025-07-12 12:24:53,497 autoreload 11636 16760 Watching for file changes with StatReloader
INFO 2025-07-12 12:25:29,527 autoreload 17356 14108 Watching for file changes with StatReloader
ERROR 2025-07-12 12:25:39,031 log 17356 14872 Internal Server Error: /ai-studio/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 539, in ai_content_studio
    return redirect('login')
           ^^^^^^^^
NameError: name 'redirect' is not defined
ERROR 2025-07-12 12:25:39,053 basehttp 17356 14872 "GET /ai-studio/ HTTP/1.1" 500 71572
WARNING 2025-07-12 12:25:41,208 log 17356 14872 Not Found: /favicon.ico
WARNING 2025-07-12 12:25:41,211 basehttp 17356 14872 "GET /favicon.ico HTTP/1.1" 404 5578
INFO 2025-07-12 12:25:43,449 autoreload 17356 14108 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:25:48,500 autoreload 6012 6836 Watching for file changes with StatReloader
INFO 2025-07-12 12:26:03,092 basehttp 6012 16944 - Broken pipe from ('127.0.0.1', 54628)
INFO 2025-07-12 12:26:04,422 basehttp 6012 17340 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:05,729 basehttp 6012 17340 "GET /static/css/style.css HTTP/1.1" 200 5462
INFO 2025-07-12 12:26:06,131 basehttp 6012 15812 "GET /static/images/logoo.png HTTP/1.1" 200 7079
INFO 2025-07-12 12:26:08,122 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6013
INFO 2025-07-12 12:26:14,055 basehttp 6012 15812 "POST /login/ HTTP/1.1" 302 0
INFO 2025-07-12 12:26:14,077 basehttp 6012 15812 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:16,866 basehttp 6012 15812 "GET / HTTP/1.1" 200 5400
INFO 2025-07-12 12:26:22,238 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
ERROR 2025-07-12 12:26:25,369 log 6012 15812 Internal Server Error: /images
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_result.image_alt

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 226, in get
    context = self.get_context_data(**kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 64, in get_context_data
    images = list(Result.objects.all())
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_result.image_alt
ERROR 2025-07-12 12:26:25,414 basehttp 6012 15812 "GET /images HTTP/1.1" 500 154259
INFO 2025-07-12 12:26:27,922 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
ERROR 2025-07-12 12:26:29,425 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:26:29,452 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203501
INFO 2025-07-12 12:26:32,302 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6319
INFO 2025-07-12 12:26:33,144 basehttp 6012 15812 "GET /ai-studio/ HTTP/1.1" 200 14914
ERROR 2025-07-12 12:26:43,107 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:26:43,154 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203505
ERROR 2025-07-12 12:27:07,218 log 6012 15812 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 575, in analytics_dashboard
    posted_count = user_posts.filter(status='posted').count()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:27:07,241 basehttp 6012 15812 "GET /analytics/ HTTP/1.1" 500 136504
ERROR 2025-07-12 12:28:22,425 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:28:22,447 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 140951
ERROR 2025-07-12 12:29:22,506 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:29:22,653 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141088
ERROR 2025-07-12 12:29:54,866 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:29:54,891 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141088
ERROR 2025-07-12 12:30:00,441 log 6012 15812 Internal Server Error: /posts/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 220, in _get_response
    response = response.render()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 114, in render
    self.content = self.rendered_content
                   ^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\response.py", line 92, in rendered_content
    return template.render(context, self._request)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\backends\django.py", line 61, in render
    return self.template.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 175, in render
    return self._render(context)
           ~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 157, in render
    return compiled_parent._render(context)
           ~~~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 167, in _render
    return self.nodelist.render(context)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\loader_tags.py", line 63, in render
    result = block.nodelist.render(context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 1005, in render
    return SafeString("".join([node.render_annotated(context) for node in self]))
                               ~~~~~~~~~~~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\base.py", line 966, in render_annotated
    return self.render(context)
           ~~~~~~~~~~~^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\template\defaulttags.py", line 320, in render
    if match:
       ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 412, in __bool__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:00,480 basehttp 6012 15812 "GET /posts/ HTTP/1.1" 500 203505
INFO 2025-07-12 12:30:03,299 basehttp 6012 15812 "GET /static/css/style.css HTTP/1.1" 304 0
INFO 2025-07-12 12:30:04,410 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:07,627 log 6012 15812 Internal Server Error: /calendar/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 643, in content_calendar
    for post in posts:
                ^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:07,645 basehttp 6012 15812 "GET /calendar/ HTTP/1.1" 500 141108
INFO 2025-07-12 12:30:09,757 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:11,395 log 6012 15812 Internal Server Error: /analytics/
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_post.status

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 575, in analytics_dashboard
    posted_count = user_posts.filter(status='posted').count()
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 608, in count
    return self.query.get_count(using=self.db)
           ~~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 568, in get_count
    return obj.get_aggregation(using, {"__count": Count("*")})["__count"]
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\query.py", line 554, in get_aggregation
    result = compiler.execute_sql(SINGLE)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_post.status
ERROR 2025-07-12 12:30:11,422 basehttp 6012 15812 "GET /analytics/ HTTP/1.1" 500 136661
INFO 2025-07-12 12:30:14,276 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
ERROR 2025-07-12 12:30:16,587 log 6012 15812 Internal Server Error: /images
Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
sqlite3.OperationalError: no such column: images_result.image_alt

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\exception.py", line 55, in inner
    response = get_response(request)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\core\handlers\base.py", line 197, in _get_response
    response = wrapped_callback(request, *callback_args, **callback_kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 104, in view
    return self.dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\contrib\auth\mixins.py", line 73, in dispatch
    return super().dispatch(request, *args, **kwargs)
           ~~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 143, in dispatch
    return handler(request, *args, **kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\views\generic\base.py", line 226, in get
    context = self.get_context_data(**kwargs)
  File "C:\Users\<USER>\Desktop\summur\imagsta\images\views.py", line 64, in get_context_data
    images = list(Result.objects.all())
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 398, in __iter__
    self._fetch_all()
    ~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 1881, in _fetch_all
    self._result_cache = list(self._iterable_class(self))
                         ~~~~^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\query.py", line 91, in __iter__
    results = compiler.execute_sql(
        chunked_fetch=self.chunked_fetch, chunk_size=self.chunk_size
    )
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\models\sql\compiler.py", line 1562, in execute_sql
    cursor.execute(sql, params)
    ~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
           ~~~~~~~~~~~~~~~~~~~~~~~~~~~^
        sql, params, many=False, executor=self._execute
        ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
    )
    ^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 84, in _execute
    with self.db.wrap_database_errors:
         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\utils.py", line 91, in __exit__
    raise dj_exc_value.with_traceback(traceback) from exc_value
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
           ~~~~~~~~~~~~~~~~~~~^^^^^^^^^^^^^
  File "C:\Users\<USER>\Desktop\summur\imagsta\venv\Lib\site-packages\django\db\backends\sqlite3\base.py", line 328, in execute
    return super().execute(query, params)
           ~~~~~~~~~~~~~~~^^^^^^^^^^^^^^^
django.db.utils.OperationalError: no such column: images_result.image_alt
ERROR 2025-07-12 12:30:16,614 basehttp 6012 15812 "GET /images HTTP/1.1" 500 154259
INFO 2025-07-12 12:30:18,261 basehttp 6012 15812 "GET /login/ HTTP/1.1" 200 6644
INFO 2025-07-12 12:32:38,372 autoreload 6012 6836 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 12:32:42,724 autoreload 9768 17028 Watching for file changes with StatReloader
INFO 2025-07-12 12:32:59,918 autoreload 9768 17028 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
INFO 2025-07-12 12:33:04,652 autoreload 16020 5768 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:15,353 autoreload 16020 5768 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:20,505 autoreload 17328 10400 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:29,999 autoreload 17328 10400 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:34,489 autoreload 4184 16440 Watching for file changes with StatReloader
INFO 2025-07-12 12:33:43,348 autoreload 4184 16440 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:33:49,719 autoreload 14744 15272 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:00,248 autoreload 14744 15272 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:06,065 autoreload 4168 10848 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:19,428 autoreload 4168 10848 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:25,463 autoreload 4340 15416 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:36,565 autoreload 4340 15416 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:42,325 autoreload 6440 17316 Watching for file changes with StatReloader
INFO 2025-07-12 12:34:52,692 autoreload 6440 17316 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:34:58,557 autoreload 2356 17256 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:09,065 autoreload 2356 17256 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:14,981 autoreload 2284 7120 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:25,275 autoreload 2284 7120 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:31,270 autoreload 17208 16868 Watching for file changes with StatReloader
INFO 2025-07-12 12:35:39,863 autoreload 17208 16868 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:35:46,436 autoreload 11936 9824 Watching for file changes with StatReloader
INFO 2025-07-12 12:36:55,412 autoreload 16788 16556 Watching for file changes with StatReloader
INFO 2025-07-12 12:38:22,020 autoreload 11936 9824 C:\Users\<USER>\Desktop\summur\imagsta\images\views.py changed, reloading.
INFO 2025-07-12 12:38:28,838 autoreload 12348 14836 Watching for file changes with StatReloader
INFO 2025-07-12 12:38:39,213 autoreload 12348 14836 C:\Users\<USER>\Desktop\summur\imagsta\images\urls.py changed, reloading.
INFO 2025-07-12 12:38:44,205 autoreload 3624 3476 Watching for file changes with StatReloader
INFO 2025-07-12 12:40:48,083 autoreload 3624 3476 C:\Users\<USER>\Desktop\summur\imagsta\images\models.py changed, reloading.
