{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:reviews_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"merchantReviews": {"methods": {"delete": {"description": "Deletes merchant review.", "flatPath": "reviews/v1beta/accounts/{accountsId}/merchantReviews/{merchantReviewsId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.merchantReviews.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ID of the merchant review. Format: accounts/{account}/merchantReviews/{merchantReview}", "location": "path", "pattern": "^accounts/[^/]+/merchantReviews/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Gets a merchant review.", "flatPath": "reviews/v1beta/accounts/{accountsId}/merchantReviews/{merchantReviewsId}", "httpMethod": "GET", "id": "merchantapi.accounts.merchantReviews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ID of the merchant review. Format: accounts/{account}/merchantReviews/{merchantReview}", "location": "path", "pattern": "^accounts/[^/]+/merchantReviews/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+name}", "response": {"$ref": "MerchantReview"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Inserts a review for your Merchant Center account. If the review already exists, then the review is replaced with the new instance.", "flatPath": "reviews/v1beta/accounts/{accountsId}/merchantReviews:insert", "httpMethod": "POST", "id": "merchantapi.accounts.merchantReviews.insert", "parameterOrder": ["parent"], "parameters": {"dataSource": {"description": "Required. The data source of the [merchantreview](https://support.google.com/merchants/answer/7045996?sjid=5253581244217581976-EU) Format: `accounts/{account}/dataSources/{datasource}`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account where the merchant review will be inserted. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+parent}/merchantReviews:insert", "request": {"$ref": "MerchantReview"}, "response": {"$ref": "MerchantReview"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists merchant reviews.", "flatPath": "reviews/v1beta/accounts/{accountsId}/merchantReviews", "httpMethod": "GET", "id": "merchantapi.accounts.merchantReviews.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of merchant reviews to return. The service can return fewer than this value. The maximum value is 1000; values above 1000 are coerced to 1000. If unspecified, the maximum number of reviews is returned.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListMerchantReviews` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListMerchantReviews` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account to list merchant reviews for. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+parent}/merchantReviews", "response": {"$ref": "ListMerchantReviewsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "productReviews": {"methods": {"delete": {"description": "Deletes a product review.", "flatPath": "reviews/v1beta/accounts/{accountsId}/productReviews/{productReviewsId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.productReviews.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ID of the Product review. Format: accounts/{account}/productReviews/{productReview}", "location": "path", "pattern": "^accounts/[^/]+/productReviews/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Gets a product review.", "flatPath": "reviews/v1beta/accounts/{accountsId}/productReviews/{productReviewsId}", "httpMethod": "GET", "id": "merchantapi.accounts.productReviews.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The ID of the merchant review. Format: accounts/{account}/productReviews/{productReview}", "location": "path", "pattern": "^accounts/[^/]+/productReviews/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+name}", "response": {"$ref": "ProductReview"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Inserts a product review.", "flatPath": "reviews/v1beta/accounts/{accountsId}/productReviews:insert", "httpMethod": "POST", "id": "merchantapi.accounts.productReviews.insert", "parameterOrder": ["parent"], "parameters": {"dataSource": {"description": "Required. Format: `accounts/{account}/dataSources/{datasource}`.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account where the product review will be inserted. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+parent}/productReviews:insert", "request": {"$ref": "ProductReview"}, "response": {"$ref": "ProductReview"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists product reviews.", "flatPath": "reviews/v1beta/accounts/{accountsId}/productReviews", "httpMethod": "GET", "id": "merchantapi.accounts.productReviews.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of products to return. The service may return fewer than this value.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListProductReviews` call. Provide this to retrieve the subsequent page. When paginating, all other parameters provided to `ListProductReviews` must match the call that provided the page token.", "location": "query", "type": "string"}, "parent": {"description": "Required. The account to list product reviews for. Format: accounts/{account}", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "reviews/v1beta/{+parent}/productReviews", "response": {"$ref": "ListProductReviewsResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"CustomAttribute": {"description": "A message that represents custom attributes. Exactly one of `value` or `group_values` must not be empty.", "id": "CustomAttribute", "properties": {"groupValues": {"description": "Subattributes within this attribute group. If `group_values` is not empty, `value` must be empty.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "name": {"description": "The name of the attribute.", "type": "string"}, "value": {"description": "The value of the attribute. If `value` is not empty, `group_values` must be empty.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "ListMerchantReviewsResponse": {"description": "Response message for the `ListMerchantsReview` method.", "id": "ListMerchantReviewsResponse", "properties": {"merchantReviews": {"description": "The merchant review.", "items": {"$ref": "MerchantReview"}, "type": "array"}, "nextPageToken": {"description": "The token to retrieve the next page of results.", "type": "string"}}, "type": "object"}, "ListProductReviewsResponse": {"description": "response message for the ListProductReviews method.", "id": "ListProductReviewsResponse", "properties": {"nextPageToken": {"description": "A token, which can be sent as `page_token` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}, "productReviews": {"description": "The product review.", "items": {"$ref": "ProductReview"}, "type": "array"}}, "type": "object"}, "MerchantReview": {"description": "A review for a merchant. For more information, see [Introduction to Merchant Review Feeds](https://developers.google.com/merchant-review-feeds)", "id": "MerchantReview", "properties": {"attributes": {"$ref": "MerchantReviewAttributes", "description": "Optional. A list of merchant review attributes."}, "customAttributes": {"description": "Optional. A list of custom (merchant-provided) attributes. It can also be used for submitting any attribute of the data specification in its generic form (for example, `{ \"name\": \"size type\", \"value\": \"regular\" }`). This is useful for submitting attributes not explicitly exposed by the API, such as experimental attributes. Maximum allowed number of characters for each custom attribute is 10240 (represents sum of characters for name and value). Maximum 2500 custom attributes can be set per product, with total size of 102.4kB. Underscores in custom attribute names are replaced by spaces upon insertion.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "dataSource": {"description": "Output only. The primary data source of the merchant review.", "readOnly": true, "type": "string"}, "merchantReviewId": {"description": "Required. The user provided merchant review ID to uniquely identify the merchant review.", "type": "string"}, "merchantReviewStatus": {"$ref": "MerchantReviewStatus", "description": "Output only. The status of a merchant review, data validation issues, that is, information about a merchant review computed asynchronously.", "readOnly": true}, "name": {"description": "Identifier. The name of the merchant review. Format: `\"{merchantreview.name=accounts/{account}/merchantReviews/{merchantReview}}\"`", "type": "string"}}, "type": "object"}, "MerchantReviewAttributes": {"description": "Attributes.", "id": "MerchantReviewAttributes", "properties": {"collectionMethod": {"description": "Optional. The method used to collect the review.", "enum": ["COLLECTION_METHOD_UNSPECIFIED", "MERCHANT_UNSOLICITED", "POINT_OF_SALE", "AFTER_FULFILLMENT"], "enumDescriptions": ["Collection method unspecified.", "The user was not responding to a specific solicitation when they submitted the review.", "The user submitted the review in response to a solicitation when the user placed an order.", "The user submitted the review in response to a solicitation after fulfillment of the user's order."], "type": "string"}, "content": {"description": "Required. This should be any freeform text provided by the user and should not be truncated. If multiple responses to different questions are provided, all responses should be included, with the minimal context for the responses to make sense. Context should not be provided if questions were left unanswered.", "type": "string"}, "isAnonymous": {"description": "Optional. Set to true if the reviewer should remain anonymous.", "type": "boolean"}, "maxRating": {"description": "Optional. The maximum possible number for the rating. The value of the max rating must be greater than the value of the min rating.", "format": "int64", "type": "string"}, "merchantDisplayName": {"description": "Optional. Human-readable display name for the merchant.", "type": "string"}, "merchantId": {"description": "Required. Must be unique and stable across all requests. In other words, if a request today and another 90 days ago refer to the same merchant, they must have the same id.", "type": "string"}, "merchantLink": {"description": "Optional. URL to the merchant's main website. Do not use a redirect URL for this value. In other words, the value should point directly to the merchant's site.", "type": "string"}, "merchantRatingLink": {"description": "Optional. URL to the landing page that hosts the reviews for this merchant. Do not use a redirect URL.", "type": "string"}, "minRating": {"description": "Optional. The minimum possible number for the rating. This should be the worst possible rating and should not be a value for no rating.", "format": "int64", "type": "string"}, "rating": {"description": "Optional. The reviewer's overall rating of the merchant.", "format": "double", "type": "number"}, "reviewCountry": {"description": "Optional. The country where the reviewer made the order defined by ISO 3166-1 Alpha-2 Country Code.", "type": "string"}, "reviewLanguage": {"description": "Optional. The language of the review defined by BCP-47 language code.", "type": "string"}, "reviewTime": {"description": "Required. The timestamp indicating when the review was written.", "format": "google-datetime", "type": "string"}, "reviewerId": {"description": "Optional. A permanent, unique identifier for the author of the review in the publisher's system.", "type": "string"}, "reviewerUsername": {"description": "Optional. Display name of the review author.", "type": "string"}, "title": {"description": "Optional. The title of the review.", "type": "string"}}, "type": "object"}, "MerchantReviewDestinationStatus": {"description": "The destination status of the merchant review status.", "id": "MerchantReviewDestinationStatus", "properties": {"reportingContext": {"description": "Output only. The name of the reporting context.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/14620732).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "readOnly": true, "type": "string"}}, "type": "object"}, "MerchantReviewItemLevelIssue": {"description": "The ItemLevelIssue of the merchant review status.", "id": "MerchantReviewItemLevelIssue", "properties": {"attribute": {"description": "Output only. The attribute's name, if the issue is caused by a single attribute.", "readOnly": true, "type": "string"}, "code": {"description": "Output only. The error code of the issue.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. A short issue description in English.", "readOnly": true, "type": "string"}, "detail": {"description": "Output only. A detailed issue description in English.", "readOnly": true, "type": "string"}, "documentation": {"description": "Output only. The URL of a web page to help with resolving this issue.", "readOnly": true, "type": "string"}, "reportingContext": {"description": "Output only. The reporting context the issue applies to.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/14620732).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "readOnly": true, "type": "string"}, "resolution": {"description": "Output only. Whether the issue can be resolved by the merchant.", "readOnly": true, "type": "string"}, "severity": {"description": "Output only. How this issue affects serving of the merchant review.", "enum": ["SEVERITY_UNSPECIFIED", "NOT_IMPACTED", "DISAPPROVED"], "enumDescriptions": ["Not specified.", "This issue represents a warning and does not have a direct affect on the merchant review.", "Issue disapproves the merchant review."], "readOnly": true, "type": "string"}}, "type": "object"}, "MerchantReviewStatus": {"description": "The status of a merchant review, data validation issues, that is, information about a merchant review computed asynchronously.", "id": "MerchantReviewStatus", "properties": {"createTime": {"description": "Output only. Date on which the item has been created, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "readOnly": true, "type": "string"}, "destinationStatuses": {"description": "Output only. The intended destinations for the merchant review.", "items": {"$ref": "MerchantReviewDestinationStatus"}, "readOnly": true, "type": "array"}, "itemLevelIssues": {"description": "Output only. A list of all issues associated with the merchant review.", "items": {"$ref": "MerchantReviewItemLevelIssue"}, "readOnly": true, "type": "array"}, "lastUpdateTime": {"description": "Output only. Date on which the item has been last updated, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/14620732).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductReview": {"description": "A review for a product. For more information, see [Introduction to Product Review Feeds](https://developers.google.com/product-review-feeds)", "id": "ProductReview", "properties": {"attributes": {"$ref": "ProductReviewAttributes", "description": "Optional. A list of product review attributes."}, "customAttributes": {"description": "Optional. A list of custom (merchant-provided) attributes.", "items": {"$ref": "CustomAttribute"}, "type": "array"}, "dataSource": {"description": "Output only. The primary data source of the product review.", "readOnly": true, "type": "string"}, "name": {"description": "Identifier. The name of the product review. Format: `\"{productreview.name=accounts/{account}/productReviews/{productReview}}\"`", "type": "string"}, "productReviewId": {"description": "Required. The permanent, unique identifier for the product review in the publisher’s system.", "type": "string"}, "productReviewStatus": {"$ref": "ProductReviewStatus", "description": "Output only. The status of a product review, data validation issues, that is, information about a product review computed asynchronously.", "readOnly": true}}, "type": "object"}, "ProductReviewAttributes": {"description": "Attributes.", "id": "ProductReviewAttributes", "properties": {"aggregatorName": {"description": "Optional. The name of the aggregator of the product reviews. A publisher may use a reviews aggregator to manage reviews and provide the feeds. This element indicates the use of an aggregator and contains information about the aggregator.", "type": "string"}, "asins": {"description": "Optional. Contains ASINs (Amazon Standard Identification Numbers) associated with a product.", "items": {"type": "string"}, "type": "array"}, "brands": {"description": "Optional. Contains brand names associated with a product.", "items": {"type": "string"}, "type": "array"}, "collectionMethod": {"description": "Optional. The method used to collect the review.", "enum": ["COLLECTION_METHOD_UNSPECIFIED", "UNSOLICITED", "POST_FULFILLMENT"], "enumDescriptions": ["Collection method unspecified.", "The user was not responding to a specific solicitation when they submitted the review.", "The user submitted the review in response to a solicitation after fulfillment of the user's order."], "type": "string"}, "cons": {"description": "Optional. Contains the disadvantages based on the opinion of the reviewer. Omit boilerplate text like \"con:\" unless it was written by the reviewer.", "items": {"type": "string"}, "type": "array"}, "content": {"description": "Optional. The content of the review. If empty, the content might still get populated from pros and cons.", "type": "string"}, "gtins": {"description": "Optional. Contains GTINs (global trade item numbers) associated with a product. Sub-types of GTINs (e.g. UPC, EAN, ISBN, JAN) are supported.", "items": {"type": "string"}, "type": "array"}, "isSpam": {"description": "Optional. Indicates whether the review is marked as spam in the publisher's system.", "type": "boolean"}, "maxRating": {"description": "Optional. The maximum possible number for the rating. The value of the max rating must be greater than the value of the min attribute.", "format": "int64", "type": "string"}, "minRating": {"description": "Optional. Contains the ratings associated with the review. The minimum possible number for the rating. This should be the worst possible rating and should not be a value for no rating.", "format": "int64", "type": "string"}, "mpns": {"description": "Optional. Contains MPNs (manufacturer part numbers) associated with a product.", "items": {"type": "string"}, "type": "array"}, "productLinks": {"description": "Optional. The URI of the product. This URI can have the same value as the `review_link` element, if the review URI and the product URI are the same.", "items": {"type": "string"}, "type": "array"}, "productNames": {"description": "Optional. Descriptive name of a product.", "items": {"type": "string"}, "type": "array"}, "pros": {"description": "Optional. Contains the advantages based on the opinion of the reviewer. Omit boilerplate text like \"pro:\" unless it was written by the reviewer.", "items": {"type": "string"}, "type": "array"}, "publisherFavicon": {"description": "Optional. A link to the company favicon of the publisher. The image dimensions should be favicon size: 16x16 pixels. The image format should be GIF, JPG or PNG.", "type": "string"}, "publisherName": {"description": "Optional. The name of the publisher of the product reviews. The information about the publisher, which may be a retailer, manufacturer, reviews service company, or any entity that publishes product reviews.", "type": "string"}, "rating": {"description": "Optional. The reviewer's overall rating of the product.", "format": "double", "type": "number"}, "reviewCountry": {"description": "Optional. The country of the review defined by ISO 3166-1 Alpha-2 Country Code.", "type": "string"}, "reviewLanguage": {"description": "Optional. The language of the review defined by BCP-47 language code.", "type": "string"}, "reviewLink": {"$ref": "ReviewLink", "description": "Optional. The URI of the review landing page."}, "reviewTime": {"description": "Required. The timestamp indicating when the review was written.", "format": "google-datetime", "type": "string"}, "reviewerId": {"description": "Optional. The author of the product review. A permanent, unique identifier for the author of the review in the publisher's system.", "type": "string"}, "reviewerImageLinks": {"description": "Optional. A URI to an image of the reviewed product created by the review author. The URI does not have to end with an image file extension.", "items": {"type": "string"}, "type": "array"}, "reviewerIsAnonymous": {"description": "Optional. Set to true if the reviewer should remain anonymous.", "type": "boolean"}, "reviewerUsername": {"description": "Optional. The name of the reviewer of the product review.", "type": "string"}, "skus": {"description": "Optional. Contains SKUs (stock keeping units) associated with a product. Often this matches the product Offer Id in the product feed.", "items": {"type": "string"}, "type": "array"}, "subclientName": {"description": "Optional. The name of the subclient of the product reviews. The subclient is an identifier of the product review source. It should be equivalent to the directory provided in the file data source path.", "type": "string"}, "title": {"description": "Optional. The title of the review.", "type": "string"}, "transactionId": {"description": "Optional. A permanent, unique identifier for the transaction associated with the review in the publisher's system. This ID can be used to indicate that multiple reviews are associated with the same transaction.", "type": "string"}}, "type": "object"}, "ProductReviewDestinationStatus": {"description": "The destination status of the product review status.", "id": "ProductReviewDestinationStatus", "properties": {"reportingContext": {"description": "Output only. The name of the reporting context.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/14620732).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "readOnly": true, "type": "string"}}, "type": "object"}, "ProductReviewItemLevelIssue": {"description": "The ItemLevelIssue of the product review status.", "id": "ProductReviewItemLevelIssue", "properties": {"attribute": {"description": "Output only. The attribute's name, if the issue is caused by a single attribute.", "readOnly": true, "type": "string"}, "code": {"description": "Output only. The error code of the issue.", "readOnly": true, "type": "string"}, "description": {"description": "Output only. A short issue description in English.", "readOnly": true, "type": "string"}, "detail": {"description": "Output only. A detailed issue description in English.", "readOnly": true, "type": "string"}, "documentation": {"description": "Output only. The URL of a web page to help with resolving this issue.", "readOnly": true, "type": "string"}, "reportingContext": {"description": "Output only. The reporting context the issue applies to.", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/14620732).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "readOnly": true, "type": "string"}, "resolution": {"description": "Output only. Whether the issue can be resolved by the merchant.", "readOnly": true, "type": "string"}, "severity": {"description": "Output only. How this issue affects serving of the product review.", "enum": ["SEVERITY_UNSPECIFIED", "NOT_IMPACTED", "DISAPPROVED"], "enumDescriptions": ["Not specified.", "This issue represents a warning and does not have a direct affect on the product review.", "Issue disapproves the product review."], "readOnly": true, "type": "string"}}, "type": "object"}, "ProductReviewStatus": {"description": "Product review status.", "id": "ProductReviewStatus", "properties": {"createTime": {"description": "Output only. Date on which the item has been created, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "readOnly": true, "type": "string"}, "destinationStatuses": {"description": "Output only. The intended destinations for the product review.", "items": {"$ref": "ProductReviewDestinationStatus"}, "readOnly": true, "type": "array"}, "itemLevelIssues": {"description": "Output only. A list of all issues associated with the product review.", "items": {"$ref": "ProductReviewItemLevelIssue"}, "readOnly": true, "type": "array"}, "lastUpdateTime": {"description": "Output only. Date on which the item has been last updated, in [ISO 8601](http://en.wikipedia.org/wiki/ISO_8601) format.", "format": "google-datetime", "readOnly": true, "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}, "ReviewLink": {"description": "The URI of the review landing page.", "id": "ReviewLink", "properties": {"link": {"description": "Optional. The URI of the review landing page. For example: `http://www.example.com/review_5.html`.", "type": "string"}, "type": {"description": "Optional. Type of the review URI.", "enum": ["TYPE_UNSPECIFIED", "SINGLETON", "GROUP"], "enumDescriptions": ["Type unspecified.", "The review page contains only this single review.", "The review page contains a group of reviews including this review."], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "reviews_v1beta", "version_module": true}