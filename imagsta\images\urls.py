from django.urls import path
from django.views.decorators.cache import cache_page
from . import views
from django.contrib.auth.views import LogoutView


#  cache_page(5*60)

urlpatterns = [
    path('',cache_page(5*60)(views.IndexView.as_view()), name='index'),
    path('images', views.ImagesView.as_view(), name='images'),
    path('login/', views.Login.as_view(), name='login'),
    path('logout/', LogoutView.as_view(), name='logout'),
    path("register/", views.RegisterView.as_view(), name="register"),
    path("search/", views.search_for_images, name="search"),
    path("posts/", views.PostsView.as_view(), name="posts"),
    path('new_feed/', views.new_feed, name="new-feed"),
    path('create_custom/', views.CustomView.as_view(), name='create-custom'),
    path('get_hashtags', views.get_hashtags, name='get-hash'),
    path('ai-studio/', views.ai_content_studio, name='ai-studio'),
    path('analytics/', views.analytics_dashboard, name='analytics'),
    path('calendar/', views.content_calendar, name='calendar'),
    path('multi-platform/', views.multi_platform_post, name='multi-platform'),
    path('templates/', views.content_templates, name='templates'),
    path('templates/preview/<int:template_id>/', views.template_preview, name='template-preview'),
    path('bulk-upload/', views.bulk_upload, name='bulk-upload'),
    path('scheduler/', views.advanced_scheduler, name='scheduler'),
]

htmx_urlpatterns = [
    path('check_username/', views.check_username, name='check-username'),
    path('search_images/', views.search_images, name='search-image'),
    # path('delete_image/<int:pk>/', views.delete_image, name='delete-image'),
    path('delete_post/<int:pk>/', views.delete_post, name='delete-post'),
    path('create_post/', views.image_to_post, name='create-post'),
    path('create_post1/', views.url_to_post, name='create-post1'),
    path('post', views.upload_post, name="upload-post"),
    path('load_feed', views.get_feed, name='get-feed'),
    path('generate-ai-content/', views.generate_ai_content, name='generate-ai-content'),

]

urlpatterns += htmx_urlpatterns