@import url("https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css");
@import url('https://fonts.googleapis.com/css2?family=Roboto+Mono&display=swap');

body {
    background-color: rgba(31, 37, 45, 0.9);
    font-family: 'Roboto Mono', monospace;
}

#navbar {
    height: 100px;
}

.success {
    color: green;
    transition: all ease-in .3s;
}

.error {
    color: red;
    transition: all ease-in .3s;
}

.card {
    transition: box-shadow .3s;
    border: 3px solid black;
    background-color: rgba(0, 0, 0, 0.445);
}

.card-body{
    padding: .5rem;
}

.card img {
    height: 250px;
}

.card:hover {
    box-shadow: 5px 5px  4px rgba(0, 0, 0, 0.288); 
    border: 3px solid #00000069;
    background-color: #00000069;
}

button#results {
    border-radius: 0 20px 20px 0;
    
}

.album{
    border-radius: 20px;
}

#search {
    border-radius: 20px 0 0 20px;
}

button.btn:hover {
    box-shadow: 2px 2px 3px black;
}



i {
    padding: .8em;
}


.htmx-indicator{
    opacity:0;
    transition: opacity 500ms ease-in;
}
.htmx-indicat{
    opacity: 0;
    display: none;
}

.htmx-request.htmx-indicat{
    display: block;
    transition: opacity 1s ease-in;
    opacity: 1;
    padding-left: 2em;
}

.htmx-request .htmx-indicator{
    opacity:1
}
.htmx-request.htmx-indicator{
    opacity:1
}

form.input-group {

    width: 80%;
    height: 20%;
    margin: 1em auto;
}

#index-card {
    height: 15em;

}

#news_item:hover {
    background-color: #373232cf !important;
}

#news_item p {
    margin: 0 0 0.3rem 0;
}

h2.h2 {
    margin: 0;
    border-radius: 20px 20px 0 0 ;
    background-image: url('../images/tech.jpg') ;
    background-position: center;
}


.list-group-item:first-child {
    border-radius: 0;
}

li span {
    position: absolute;
    margin: -1em;
    background-color: #dc3545;
    width: 6.7%;
    border-radius: 0 0 17px;
    text-align: center;
    padding: .2em;
}

li#news_item p {
    margin: 1.4em 0 .7em 0;
}

#feed-title {
    /* border: 2px #dc3545 solid; */
    height: 40%;
    padding: 1em;
    border-radius: 10px;
    color: white;
}