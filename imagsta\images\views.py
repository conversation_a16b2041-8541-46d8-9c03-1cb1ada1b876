from email import header
from django.core.files import File
from django.http.response import HttpResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.http import require_http_methods
from django.shortcuts import render
from django.contrib.auth.views import LoginView
from django.urls import reverse_lazy
from bs4 import BeautifulSoup
from django.views.generic import FormView, TemplateView, ListView
import urllib
from django.contrib.auth import get_user_model
from PIL import Image
from h11 import Data
from pygooglenews import GoogleNews
import random
import time
import requests
import io
import uuid
from .models import *
from .utils import getContentPublishingLimit, make_post, image_to_url, getCreds, createMediaObject, publishMedia
from images.forms import RegisterForm


class IndexView(TemplateView):
    template_name = 'index.html'
    def get_context_data(self, **kwargs):
        context = super(IndexView, self).get_context_data(**kwargs)

        # feed = get_feed()
        gn = GoogleNews()
        business = gn.topic_headlines('business')
        world = gn.topic_headlines('world')
        technology = gn.topic_headlines('technology')
        context = {
            'b_headlines': business,
            # 'b_headlines': True,
            'w_headlines': world,
            # # 'w_headlines': 1,
            't_headlines': technology,
            # 't_headlines': 2,
        }
        return context

class ImagesView(LoginRequiredMixin, TemplateView):
    template_name = 'images.html'

    def get_context_data(self, **kwargs):
        context = super(ImagesView, self).get_context_data(**kwargs)
        images = list(Result.objects.all())
        random.shuffle(images)
        context['images']= images[:12]
        return context

    
class Login(LoginView):
    template_name = 'registration/login.html'

class RegisterView(FormView):
    form_class = RegisterForm
    template_name = 'registration/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        form.save()  # save the user
        return super().form_valid(form)


@require_http_methods(['DELETE'])
def delete_image(request, pk):
    Result.objects.get(pk=pk).delete()
    images = list(Result.objects.all())
    random.shuffle(images)

    context = {
        'images': images[:12]
    }
    return render(request, 'partials/test.html', context)

def check_username(request):
    username = request.POST.get('username')
    if get_user_model().objects.filter(username=username).exists():
        return HttpResponse("<div id='username-error' class='error'>This username already exists</div>")
    else:
        return HttpResponse("<div id='username-error' class='success'>This username is available</div>")


def search_for_images(request):
    query = request.POST.get('search')
    feed = request.POST.get('feed')
    search ,created = Search.objects.get_or_create(query=query)
    if created:
        params = dict()
        params['query'] = query
        params['client_id'] = 'bTzoNhm_hwwysZDSLsS5BDNAjeRRlK2Lz1qDEr8zDYc'
        url = 'https://api.unsplash.com/search/photos'
        data = requests.get(url, params)
        data = data.json()
        images = []
        dimages = []
        for obj in data['results']:
            image = obj['urls']['full']
            dis = obj['urls']['small']
            dimages.append(dis)
            images.append(image)
            r = Result(query=search, image=image, dis_image=dis)
            r.save()
        comp_list = zip(images, dimages)
            
        

        # images = GoogleImageScraper(query, 12).find_image_urls()
        # for image in images:
        #     r = Result(query=search, image=image)
        #     r.save()
    else:
        images = Result.objects.filter(query=search)

    
    context = {
        'images': comp_list,
        'feed': feed,
    }
    return render(request, 'partials/test.html', context)

def search_images(request):
    search = request.POST.get('search')
    
    results = Search.objects.filter(query=search).exists()
    
    context = {
        'results':results
    }
    return render(request, 'partials/search_result.html', context)


class PostsView(ListView):
    model = Post
    template_name = "posts.html"
    context_object_name = 'posts'

    def get_queryset(self):
        return Post.objects.filter(user=self.request.user).order_by('-id')[:21]

def image_text(request):
    print(request.POST.get('image'))
    print(request.POST.get('feed'))
    return render(request, 'partials/image_result.html')

def image_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')
    print(image_url)
    image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
    post = make_post(image, feed)
    tempfile_io = io.BytesIO()
    post.save(tempfile_io, format='JPEG', quality=100)
    name = uuid.uuid4()
    image_file = File(tempfile_io, name=str(name)+'.jpeg')
    post = Post(user=request.user, image=image_file, post_text=feed)
    post.image = image_file
    post.save()
    context = {
        'post_url': post.image
    }
    return render(request, 'partials/image_result.html',context)


def url_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')
    print(image_url)
    image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
    post = make_post(image, feed)
    tempfile_io = io.BytesIO()
    post.save(tempfile_io, format='JPEG', quality=100)
    name = uuid.uuid4()
    image_file = File(tempfile_io, name=str(name)+'.jpeg')
    post = Post(user=request.user, image=image_file, post_text=feed)
    post.image = image_file
    post.save()
    return HttpResponse('<button id="edit-btn" class="btn btn-outline-danger" disabled type="submit">Edited!</button>')


def new_feed(request):
    title = request.POST.get('title')
    context = {
        'title': title,
    }
    return render(request, 'new_feed.html', context)


def upload_post(request):
    image = request.POST.get('image')
    id = request.POST.get('id')    
    hashtags = request.POST.get('hashtags')
    caption = request.POST.get('caption')
    image_url = image_to_url(image)
    print(image_url)
    
    params = getCreds() # get creds from defines

    params['media_type'] = 'IMAGE' # type of asset
    params['media_url'] = image_url 
    if caption:
        params['caption'] = caption
    else:
        params['caption'] = ''
    params['caption'] += '\n'
    params['caption'] += '\n'
    params['caption'] += '\n'
    params['caption'] += hashtags # caption for the post


    imageMediaObjectResponse = createMediaObject( params ) # create a media object through the api
    print(imageMediaObjectResponse)
    imageMediaObjectId = imageMediaObjectResponse['json_data']['id'] # id of the media object that was created


    print( "\tID:" ) # label
    print( "\t" + imageMediaObjectId ) # id of the object

    
    publishImageResponse = publishMedia( imageMediaObjectId, params ) # publish the post to instagram
    

    print( "\tResponse:" ) # label
    print( publishImageResponse['json_data_pretty'] ) # json response from ig api
    
    post = Post.objects.get(pk=id)
    post.published_image_id = publishImageResponse['json_data']['id']
    post.posted = True
    post.hashtags = hashtags
    post.save()

    posts = Post.objects.filter(user=request.user).order_by('-id')[:21]
    context = {
        'posts': posts
    }

    return render(request, 'partials/posts.html', context)


class CustomView(TemplateView):
    template_name = 'custom_post.html'

@require_http_methods(['DELETE'])
def delete_post(request, pk):
    Post.objects.get(pk=pk).delete()
    images = list(Post.objects.filter(user=request.user).order_by('-id')[:21])


    context = {
        'posts': images[:21]
    }
    return render(request, 'partials/posts.html', context)


def get_feed():

    url = "https://collider.com"
    headers = requests.utils.default_headers()

    headers.update(
        {
            'User-Agent': 'My User Agent 1.0',
        }
    )
    result = requests.get(url=url, headers=headers)

    doc = BeautifulSoup(result.text, "html.parser")
    feeds = doc.find_all("a", class_="bc-title-link")
    news = []
    for feed in feeds:
        news.append(feed.text)
    print(news)
    return news


def get_hashtags(request):
    query = request.POST.get('hash-search')
    url = "http://best-hashtags.com/hashtag/"+query
    result = requests.get(url)
    doc = BeautifulSoup(result.text, "html.parser")
    hashtags = doc.find('p1')
    context = {
         'hashtags':hashtags.text
    }
    return HttpResponse(hashtags.text)  