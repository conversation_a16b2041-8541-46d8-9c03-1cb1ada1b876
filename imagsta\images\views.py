import logging
import random
import time
import requests
import io
import uuid
from django.core.files import File
from django.http.response import HttpResponse
from django.http import JsonResponse
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.decorators.http import require_http_methods
from django.shortcuts import render, redirect
from django.contrib.auth.views import LoginView
from django.urls import reverse_lazy
from django.views.generic import FormView, TemplateView, ListView
from django.contrib.auth import get_user_model
from django.contrib import messages
from django.conf import settings
from bs4 import BeautifulSoup
from PIL import Image
from pygooglenews import GoogleNews
from decouple import config
from .models import User, Search, Result, Post
from .utils import getContentPublishingLimit, make_post, image_to_url, getCreds, createMediaObject, publishMedia
from .forms import RegisterForm
from .ai_services import ai_service

# Set up logging
logger = logging.getLogger(__name__)


class IndexView(TemplateView):
    template_name = 'index.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        try:
            gn = GoogleNews()
            business = gn.topic_headlines('business')
            world = gn.topic_headlines('world')
            technology = gn.topic_headlines('technology')

            context.update({
                'b_headlines': business,
                'w_headlines': world,
                't_headlines': technology,
            })
        except Exception as e:
            logger.error(f"Error fetching news headlines: {e}")
            context.update({
                'b_headlines': [],
                'w_headlines': [],
                't_headlines': [],
                'error_message': 'Unable to fetch news headlines at this time.'
            })

        return context

class ImagesView(LoginRequiredMixin, TemplateView):
    template_name = 'images.html'

    def get_context_data(self, **kwargs):
        context = super(ImagesView, self).get_context_data(**kwargs)
        images = list(Result.objects.all())
        random.shuffle(images)
        context['images']= images[:12]
        return context

    
class Login(LoginView):
    template_name = 'registration/login.html'

class RegisterView(FormView):
    form_class = RegisterForm
    template_name = 'registration/register.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        form.save()  # save the user
        return super().form_valid(form)


@require_http_methods(['DELETE'])
def delete_image(request, pk):
    Result.objects.get(pk=pk).delete()
    images = list(Result.objects.all())
    random.shuffle(images)

    context = {
        'images': images[:12]
    }
    return render(request, 'partials/test.html', context)

def check_username(request):
    username = request.POST.get('username')
    if get_user_model().objects.filter(username=username).exists():
        return HttpResponse("<div id='username-error' class='error'>This username already exists</div>")
    else:
        return HttpResponse("<div id='username-error' class='success'>This username is available</div>")


def search_for_images(request):
    """Search for images using Unsplash API with improved error handling and rate limiting."""
    query = request.POST.get('search', '').strip()
    feed = request.POST.get('feed', '')

    if not query:
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Please enter a search query'
        })

    if len(query) < 2:
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Search query must be at least 2 characters long'
        })

    try:
        search, created = Search.objects.get_or_create(query=query)

        if not created:
            # Increment search count for existing queries
            search.increment_search_count()

        if created or not search.results.exists():
            # Fetch new images from Unsplash
            client_id = config('UNSPLASH_CLIENT_ID', default='')
            if not client_id:
                logger.error("Unsplash client ID not configured")
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': 'Image search service not configured'
                })

            params = {
                'query': query,
                'client_id': client_id,
                'per_page': 12,
                'orientation': 'squarish',  # Better for Instagram posts
                'content_filter': 'high'    # Filter out potentially inappropriate content
            }
            url = 'https://api.unsplash.com/search/photos'

            headers = {
                'Accept-Version': 'v1',
                'User-Agent': 'Imagsta/1.0'
            }

            response = requests.get(url, params=params, headers=headers, timeout=15)
            response.raise_for_status()

            # Check rate limiting
            if response.status_code == 429:
                logger.warning("Unsplash API rate limit exceeded")
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': 'Too many requests. Please try again later.'
                })

            data = response.json()

            if not data.get('results'):
                return render(request, 'partials/test.html', {
                    'images': [],
                    'feed': feed,
                    'error': f'No images found for "{query}"'
                })

            images = []
            dimages = []

            for obj in data.get('results', []):
                try:
                    image_url = obj['urls']['full']
                    display_url = obj['urls']['small']
                    alt_text = obj.get('alt_description', '') or obj.get('description', '')

                    dimages.append(display_url)
                    images.append(image_url)

                    Result.objects.create(
                        query=search,
                        image=image_url,
                        dis_image=display_url,
                        image_alt=alt_text[:255],  # Truncate to field limit
                        source='unsplash'
                    )
                except KeyError as e:
                    logger.warning(f"Missing expected field in Unsplash response: {e}")
                    continue

            comp_list = list(zip(images, dimages))
        else:
            # Use existing results
            results = search.results.all()[:12]  # Limit to 12 results
            comp_list = [(r.image, r.dis_image) for r in results]

        context = {
            'images': comp_list,
            'feed': feed,
            'query': query,
            'total_results': len(comp_list)
        }
        return render(request, 'partials/test.html', context)

    except requests.exceptions.Timeout:
        logger.error("Timeout while fetching images from Unsplash")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Request timed out. Please try again.'
        })
    except requests.exceptions.RequestException as e:
        logger.error(f"Error fetching images from Unsplash: {e}")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'Failed to fetch images from search service'
        })
    except Exception as e:
        logger.error(f"Unexpected error in search_for_images: {e}")
        return render(request, 'partials/test.html', {
            'images': [],
            'feed': feed,
            'error': 'An unexpected error occurred'
        })

def search_images(request):
    search = request.POST.get('search')
    
    results = Search.objects.filter(query=search).exists()
    
    context = {
        'results':results
    }
    return render(request, 'partials/search_result.html', context)


class PostsView(ListView):
    model = Post
    template_name = "posts.html"
    context_object_name = 'posts'

    def get_queryset(self):
        return Post.objects.filter(user=self.request.user).order_by('-id')[:21]

def image_text(request):
    print(request.POST.get('image'))
    print(request.POST.get('feed'))
    return render(request, 'partials/image_result.html')

def image_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')
    print(image_url)
    image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
    post = make_post(image, feed)
    tempfile_io = io.BytesIO()
    post.save(tempfile_io, format='JPEG', quality=100)
    name = uuid.uuid4()
    image_file = File(tempfile_io, name=str(name)+'.jpeg')
    post = Post(user=request.user, image=image_file, post_text=feed)
    post.image = image_file
    post.save()
    context = {
        'post_url': post.image
    }
    return render(request, 'partials/image_result.html',context)


def url_to_post(request):
    image_url = request.POST.get('image')
    feed = request.POST.get('feed')
    print(image_url)
    image = Image.open(requests.get(image_url, stream=True).raw).convert('RGB')
    post = make_post(image, feed)
    tempfile_io = io.BytesIO()
    post.save(tempfile_io, format='JPEG', quality=100)
    name = uuid.uuid4()
    image_file = File(tempfile_io, name=str(name)+'.jpeg')
    post = Post(user=request.user, image=image_file, post_text=feed)
    post.image = image_file
    post.save()
    return HttpResponse('<button id="edit-btn" class="btn btn-outline-danger" disabled type="submit">Edited!</button>')


def new_feed(request):
    title = request.POST.get('title')
    context = {
        'title': title,
    }
    return render(request, 'new_feed.html', context)


def upload_post(request):
    """Upload a post to Instagram with improved error handling and validation."""
    if request.method != 'POST':
        return HttpResponse('Method not allowed', status=405)

    try:
        image_path = request.POST.get('image', '').strip()
        post_id = request.POST.get('id', '').strip()
        hashtags = request.POST.get('hashtags', '').strip()
        caption = request.POST.get('caption', '').strip()

        # Validate required fields
        if not image_path or not post_id:
            messages.error(request, 'Missing required fields')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Missing required fields'
            })

        # Get the post object
        try:
            post = Post.objects.get(pk=post_id, user=request.user)
        except Post.DoesNotExist:
            messages.error(request, 'Post not found')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Post not found'
            })

        # Upload image to hosting service
        image_url = image_to_url(image_path)
        if not image_url:
            logger.error(f"Failed to upload image to hosting service: {image_path}")
            messages.error(request, 'Failed to upload image')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to upload image'
            })

        logger.info(f"Image uploaded successfully: {image_url}")

        # Get Instagram API credentials
        creds = getCreds()
        if not creds:
            logger.error("Instagram API credentials not configured")
            messages.error(request, 'Instagram API not configured')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Instagram API not configured'
            })

        # Prepare Instagram post parameters
        creds['media_type'] = 'IMAGE'
        creds['media_url'] = image_url

        # Build caption with proper formatting
        full_caption = caption.strip() if caption else ''
        if hashtags:
            if full_caption:
                full_caption += '\n\n'
            full_caption += hashtags

        creds['caption'] = full_caption[:2200]  # Instagram caption limit

        # Create media object on Instagram
        media_response = createMediaObject(creds)
        if 'error' in media_response:
            logger.error(f"Failed to create Instagram media object: {media_response['error']}")
            messages.error(request, 'Failed to create Instagram media object')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to create Instagram media object'
            })

        media_object_id = media_response['json_data'].get('id')
        if not media_object_id:
            logger.error("No media object ID returned from Instagram API")
            messages.error(request, 'Invalid response from Instagram API')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Invalid response from Instagram API'
            })

        logger.info(f"Instagram media object created with ID: {media_object_id}")

        # Publish the post to Instagram
        publish_response = publishMedia(media_object_id, creds)
        if 'error' in publish_response:
            logger.error(f"Failed to publish to Instagram: {publish_response['error']}")
            messages.error(request, 'Failed to publish to Instagram')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Failed to publish to Instagram'
            })

        instagram_post_id = publish_response['json_data'].get('id')
        if not instagram_post_id:
            logger.error("No Instagram post ID returned")
            messages.error(request, 'Post published but no ID returned')
            return render(request, 'partials/posts.html', {
                'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
                'error': 'Post published but no ID returned'
            })

        # Update post with success information
        post.published_image_id = instagram_post_id
        post.posted = True
        post.hashtags = hashtags
        post.caption = caption
        post.save()

        logger.info(f"Post successfully published to Instagram with ID: {instagram_post_id}")
        messages.success(request, 'Post successfully published to Instagram!')

        # Return updated posts list
        posts = Post.objects.filter(user=request.user).order_by('-created')[:21]
        return render(request, 'partials/posts.html', {'posts': posts})

    except Exception as e:
        logger.error(f"Unexpected error in upload_post: {e}")
        messages.error(request, 'An unexpected error occurred')
        return render(request, 'partials/posts.html', {
            'posts': Post.objects.filter(user=request.user).order_by('-created')[:21],
            'error': 'An unexpected error occurred'
        })


class CustomView(TemplateView):
    template_name = 'custom_post.html'

@require_http_methods(['DELETE'])
def delete_post(request, pk):
    Post.objects.get(pk=pk).delete()
    images = list(Post.objects.filter(user=request.user).order_by('-id')[:21])


    context = {
        'posts': images[:21]
    }
    return render(request, 'partials/posts.html', context)


def get_feed():

    url = "https://collider.com"
    headers = requests.utils.default_headers()

    headers.update(
        {
            'User-Agent': 'My User Agent 1.0',
        }
    )
    result = requests.get(url=url, headers=headers)

    doc = BeautifulSoup(result.text, "html.parser")
    feeds = doc.find_all("a", class_="bc-title-link")
    news = []
    for feed in feeds:
        news.append(feed.text)
    print(news)
    return news


def get_hashtags(request):
    query = request.POST.get('hash-search')
    url = "http://best-hashtags.com/hashtag/"+query
    result = requests.get(url)
    doc = BeautifulSoup(result.text, "html.parser")
    hashtags = doc.find('p1')
    context = {
         'hashtags':hashtags.text
    }
    return HttpResponse(hashtags.text)


def generate_ai_content(request):
    """Generate AI-powered captions and hashtags for content."""
    if request.method != 'POST':
        return HttpResponse('Method not allowed', status=405)

    try:
        image_description = request.POST.get('image_description', '').strip()
        news_context = request.POST.get('news_context', '').strip()
        tone = request.POST.get('tone', 'professional')
        industry = request.POST.get('industry', 'general')

        if not image_description and not news_context:
            return render(request, 'partials/ai_content.html', {
                'error': 'Please provide either image description or news context'
            })

        # Generate AI content
        caption = ai_service.generate_caption(
            image_description=image_description,
            news_context=news_context,
            tone=tone
        )

        hashtags = ai_service.suggest_hashtags(
            content=caption or news_context,
            industry=industry
        )

        # Get posting time suggestions
        posting_suggestions = ai_service.optimize_posting_time({})

        # Analyze content performance
        performance_analysis = ai_service.analyze_content_performance(
            caption or news_context
        )

        context = {
            'generated_caption': caption,
            'suggested_hashtags': hashtags,
            'posting_suggestions': posting_suggestions,
            'performance_analysis': performance_analysis,
            'tone': tone,
            'industry': industry
        }

        return render(request, 'partials/ai_content.html', context)

    except Exception as e:
        logger.error(f"Error in generate_ai_content: {e}")
        return render(request, 'partials/ai_content.html', {
            'error': 'An error occurred while generating content. Please try again.'
        })


def ai_content_studio(request):
    """AI Content Studio - main page for AI-powered content creation."""
    if not request.user.is_authenticated:
        return redirect('login')

    context = {
        'tone_options': [
            ('professional', 'Professional'),
            ('casual', 'Casual'),
            ('humorous', 'Humorous'),
            ('inspirational', 'Inspirational')
        ],
        'industry_options': [
            ('general', 'General'),
            ('tech', 'Technology'),
            ('business', 'Business'),
            ('lifestyle', 'Lifestyle'),
            ('health', 'Health & Wellness'),
            ('food', 'Food & Beverage'),
            ('travel', 'Travel'),
            ('fashion', 'Fashion'),
            ('education', 'Education'),
            ('finance', 'Finance')
        ]
    }

    return render(request, 'ai_studio.html', context)


def analytics_dashboard(request):
    """Analytics dashboard showing post performance and insights."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import PostAnalytics, UserAnalytics, ScheduledPost
    from datetime import datetime, timedelta

    # Get or create user analytics
    user_analytics, created = UserAnalytics.objects.get_or_create(user=request.user)
    if created or user_analytics.last_calculated < timezone.now() - timedelta(hours=1):
        user_analytics.update_totals()

    # Get user's posts
    user_posts = Post.objects.filter(user=request.user).order_by('-created')

    # Calculate basic analytics
    total_posts = user_posts.count()
    posted_count = user_posts.filter(posted=True).count()
    draft_count = user_posts.filter(posted=False).count()
    scheduled_count = ScheduledPost.objects.filter(user=request.user, status='pending').count()

    # Recent activity (last 30 days)
    thirty_days_ago = timezone.now() - timedelta(days=30)
    recent_posts = user_posts.filter(created__gte=thirty_days_ago)

    # Get real analytics data
    post_analytics = PostAnalytics.objects.filter(post__user=request.user)

    # Calculate aggregate metrics
    total_likes = sum(pa.likes_count for pa in post_analytics)
    total_comments = sum(pa.comments_count for pa in post_analytics)
    total_shares = sum(pa.shares_count for pa in post_analytics)
    total_reach = sum(pa.reach for pa in post_analytics)
    total_impressions = sum(pa.impressions for pa in post_analytics)

    avg_engagement_rate = user_analytics.avg_engagement_rate

    analytics_data = {
        'total_likes': total_likes,
        'total_comments': total_comments,
        'total_shares': total_shares,
        'avg_engagement_rate': round(avg_engagement_rate, 2),
        'follower_growth': user_analytics.follower_growth_rate,
        'reach': total_reach,
        'impressions': total_impressions
    }

    # Get top performing posts
    top_posts = PostAnalytics.objects.filter(
        post__user=request.user
    ).order_by('-engagement_rate')[:5]

    # Posting frequency data for chart
    posting_frequency = []
    for i in range(7):
        date = timezone.now() - timedelta(days=i)
        count = user_posts.filter(
            created__date=date.date()
        ).count()
        posting_frequency.append({
            'date': date.strftime('%Y-%m-%d'),
            'count': count
        })

    # Engagement trend data
    engagement_trend = user_analytics.get_engagement_trend(30)

    # Performance insights
    insights = []
    if avg_engagement_rate > 5:
        insights.append("Your engagement rate is above average! Keep up the great work.")
    elif avg_engagement_rate < 2:
        insights.append("Consider posting more engaging content to improve your engagement rate.")

    if total_posts > 0 and posted_count / total_posts < 0.5:
        insights.append("You have many drafts. Consider publishing more content regularly.")

    if scheduled_count > 0:
        insights.append(f"You have {scheduled_count} posts scheduled for publication.")

    context = {
        'total_posts': total_posts,
        'posted_count': posted_count,
        'draft_count': draft_count,
        'scheduled_count': scheduled_count,
        'recent_posts_count': recent_posts.count(),
        'analytics': analytics_data,
        'top_posts': top_posts,
        'posting_frequency': posting_frequency,
        'engagement_trend': engagement_trend,
        'recent_posts': recent_posts[:10],
        'insights': insights,
        'user_analytics': user_analytics
    }

    return render(request, 'analytics.html', context)


def content_calendar(request):
    """Content calendar view for scheduling and managing posts."""
    if not request.user.is_authenticated:
        return redirect('login')

    # Get posts for the current month
    from datetime import datetime
    current_month = datetime.now().month
    current_year = datetime.now().year

    posts = Post.objects.filter(
        user=request.user,
        created__month=current_month,
        created__year=current_year
    ).order_by('created')

    # Group posts by date
    calendar_data = {}
    for post in posts:
        date_key = post.created.strftime('%Y-%m-%d')
        if date_key not in calendar_data:
            calendar_data[date_key] = []
        calendar_data[date_key].append(post)

    context = {
        'calendar_data': calendar_data,
        'current_month': datetime.now().strftime('%B %Y'),
        'posts': posts
    }

    return render(request, 'calendar.html', context)


def multi_platform_post(request):
    """Multi-platform posting interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .social_platforms import social_manager

    if request.method == 'POST':
        try:
            # Get form data
            content_text = request.POST.get('content_text', '')
            selected_platforms = request.POST.getlist('platforms')
            image_file = request.FILES.get('image')

            if not content_text and not image_file:
                messages.error(request, 'Please provide content text or an image')
                return redirect('multi-platform')

            # Prepare content for posting
            content = {
                'text': content_text,
                'caption': content_text  # For Instagram
            }

            # Handle image upload if provided
            if image_file:
                # Save the image temporarily
                post = Post.objects.create(
                    user=request.user,
                    image=image_file,
                    caption=content_text,
                    post_text=content_text[:130]  # Truncate for existing field
                )

                # Upload to image hosting service
                from .utils import image_to_url
                image_path = post.image.path
                image_url = image_to_url(image_path)

                if image_url:
                    content['image_url'] = image_url
                else:
                    messages.error(request, 'Failed to upload image')
                    return redirect('multi-platform')

            # Post to selected platforms
            results = social_manager.post_to_multiple_platforms(content, selected_platforms)

            # Process results
            success_count = 0
            error_messages = []

            for platform, result in results.items():
                if result.get('success'):
                    success_count += 1
                    # Update post with platform-specific IDs if image was uploaded
                    if image_file and 'post' in locals():
                        if platform == 'instagram' and result.get('post_id'):
                            post.published_image_id = result['post_id']
                            post.posted = True
                            post.save()
                else:
                    error_messages.append(f"{platform.title()}: {result.get('error', 'Unknown error')}")

            # Show results to user
            if success_count > 0:
                messages.success(request, f'Successfully posted to {success_count} platform(s)')

            if error_messages:
                for error in error_messages:
                    messages.error(request, error)

            return redirect('multi-platform')

        except Exception as e:
            logger.error(f"Multi-platform posting error: {e}")
            messages.error(request, 'An error occurred while posting')
            return redirect('multi-platform')

    # GET request - show the form
    available_platforms = social_manager.get_available_platforms()

    context = {
        'available_platforms': available_platforms,
        'platform_info': {
            'instagram': {
                'name': 'Instagram',
                'icon': 'bi-instagram',
                'color': 'danger',
                'features': ['Images', 'Captions', 'Hashtags']
            },
            'twitter': {
                'name': 'Twitter',
                'icon': 'bi-twitter',
                'color': 'info',
                'features': ['Text (280 chars)', 'Images', 'Hashtags']
            },
            'linkedin': {
                'name': 'LinkedIn',
                'icon': 'bi-linkedin',
                'color': 'primary',
                'features': ['Professional content', 'Articles', 'Images']
            }
        }
    }

    return render(request, 'multi_platform.html', context)


def content_templates(request):
    """Content templates management interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ContentTemplate

    # Handle template creation
    if request.method == 'POST':
        try:
            template = ContentTemplate.objects.create(
                user=request.user,
                name=request.POST.get('name'),
                template_type=request.POST.get('template_type'),
                content=request.POST.get('content'),
                industry=request.POST.get('industry', ''),
                tone=request.POST.get('tone', ''),
                hashtags=request.POST.get('hashtags', ''),
                is_public=request.POST.get('is_public') == 'on'
            )
            messages.success(request, f'Template "{template.name}" created successfully!')
            return redirect('templates')
        except Exception as e:
            logger.error(f"Error creating template: {e}")
            messages.error(request, 'Error creating template. Please try again.')

    # Get templates from database
    templates = ContentTemplate.objects.filter(user=request.user)

    # Add public templates
    public_templates = ContentTemplate.objects.filter(is_public=True).exclude(user=request.user)

    # Filter by industry if requested
    industry_filter = request.GET.get('industry', '')
    if industry_filter:
        templates = templates.filter(industry=industry_filter)
        public_templates = public_templates.filter(industry=industry_filter)

    # Filter by type if requested
    type_filter = request.GET.get('type', '')
    if type_filter:
        templates = templates.filter(template_type=type_filter)
        public_templates = public_templates.filter(template_type=type_filter)

    # Combine user templates and public templates
    all_templates = list(templates) + list(public_templates[:10])  # Limit public templates

    # If no templates exist, create some default ones
    if not templates.exists() and not ContentTemplate.objects.filter(user=request.user).exists():
        _create_default_templates(request.user)
        templates = ContentTemplate.objects.filter(user=request.user)
        all_templates = list(templates)

    context = {
        'templates': all_templates,
        'user_templates_count': templates.count(),
        'public_templates_count': public_templates.count(),
        'industries': ContentTemplate.INDUSTRIES,
        'template_types': ContentTemplate.TEMPLATE_TYPES,
        'tones': ContentTemplate.TONES,
        'current_industry': industry_filter,
        'current_type': type_filter,
    }

    return render(request, 'content_templates.html', context)


def _create_default_templates(user):
    """Create default templates for new users."""
    from .models import ContentTemplate

    default_templates = [
        {
            'name': 'Product Launch',
            'template_type': 'campaign',
            'content': '🚀 Exciting news! We\'re launching {product_name}! {description} #NewProduct #Launch #Innovation',
            'industry': 'technology',
            'tone': 'professional',
            'hashtags': '#NewProduct #Launch #Innovation #Tech'
        },
        {
            'name': 'Behind the Scenes',
            'template_type': 'caption',
            'content': '👀 Behind the scenes at {company_name}! Here\'s how we {activity}. What would you like to see next? #BehindTheScenes',
            'industry': 'business',
            'tone': 'casual',
            'hashtags': '#BehindTheScenes #TeamWork #Process'
        },
        {
            'name': 'Motivational Monday',
            'template_type': 'post_series',
            'content': '💪 Monday Motivation: {quote} Remember, {advice}. What\'s your goal this week? #MotivationalMonday',
            'industry': 'lifestyle',
            'tone': 'inspirational',
            'hashtags': '#MotivationalMonday #Inspiration #Goals #Success'
        }
    ]

    for template_data in default_templates:
        ContentTemplate.objects.create(user=user, **template_data)


def template_preview(request, template_id):
    """Preview a template with sample variables."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import ContentTemplate

    try:
        # Get template from database
        template = ContentTemplate.objects.get(
            id=template_id,
            user=request.user
        )
    except ContentTemplate.DoesNotExist:
        # Try to get public template
        try:
            template = ContentTemplate.objects.get(id=template_id, is_public=True)
        except ContentTemplate.DoesNotExist:
            return JsonResponse({'error': 'Template not found'}, status=404)

    # Sample data for preview
    sample_data = {
        'product_name': 'Amazing Widget Pro',
        'description': 'The ultimate solution for productivity',
        'company_name': 'TechCorp',
        'activity': 'create innovative solutions',
        'quote': '"Success is not final, failure is not fatal: it is the courage to continue that counts."',
        'advice': 'every small step counts towards your bigger goals',
        'dish_name': 'Gourmet Pasta',
        'ingredients': 'fresh tomatoes, basil, and mozzarella',
        'secret': 'a touch of love',
        'event_name': 'Summer Festival',
        'location': 'Central Park',
        'date': 'July 15th'
    }

    try:
        preview_content = template.render_content(sample_data)
    except Exception as e:
        preview_content = template.content
        logger.warning(f"Error rendering template preview: {e}")

    return JsonResponse({
        'name': template.name,
        'content': template.content,
        'preview': preview_content,
        'variables': template.variables,
        'hashtags': template.hashtags,
        'industry': template.industry,
        'tone': template.tone,
        'usage_count': template.usage_count
    })


def bulk_upload(request):
    """Bulk upload and scheduling interface."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ScheduledPost, PostAnalytics
    from datetime import datetime, timedelta

    if request.method == 'POST':
        try:
            # Handle multiple file uploads
            uploaded_files = request.FILES.getlist('images')
            captions = request.POST.getlist('captions')
            hashtags_list = request.POST.getlist('hashtags')
            schedule_times = request.POST.getlist('schedule_times')
            schedule_type = request.POST.get('schedule_type', 'manual')

            if not uploaded_files:
                messages.error(request, 'Please select at least one image to upload')
                return redirect('bulk-upload')

            if len(uploaded_files) > 10:
                messages.error(request, 'Maximum 10 files allowed per upload')
                return redirect('bulk-upload')

            created_posts = []
            scheduled_posts = []

            for i, image_file in enumerate(uploaded_files):
                # Validate file size (max 10MB)
                if image_file.size > 10 * 1024 * 1024:
                    messages.warning(request, f'File {image_file.name} is too large (max 10MB)')
                    continue

                # Validate file type
                if not image_file.content_type.startswith('image/'):
                    messages.warning(request, f'File {image_file.name} is not an image')
                    continue

                # Get corresponding data for this image
                caption = captions[i] if i < len(captions) else ''
                hashtags = hashtags_list[i] if i < len(hashtags_list) else ''
                schedule_time = schedule_times[i] if i < len(schedule_times) else None

                # Create post
                post = Post.objects.create(
                    user=request.user,
                    image=image_file,
                    caption=caption,
                    hashtags=hashtags,
                    post_text=caption[:130]  # Truncate for existing field
                )

                # Create analytics record
                PostAnalytics.objects.create(post=post)

                created_posts.append(post)

                # Handle scheduling
                if schedule_time and schedule_type != 'manual':
                    try:
                        scheduled_datetime = datetime.fromisoformat(schedule_time.replace('Z', '+00:00'))
                        scheduled_post = ScheduledPost.objects.create(
                            user=request.user,
                            post=post,
                            scheduled_for=scheduled_datetime,
                            target_platforms=['instagram']  # Default platform
                        )
                        scheduled_posts.append(scheduled_post)
                    except ValueError:
                        logger.warning(f"Invalid schedule time format: {schedule_time}")

            # Success message
            success_msg = f'Successfully uploaded {len(created_posts)} posts!'
            if scheduled_posts:
                success_msg += f' {len(scheduled_posts)} posts scheduled for publication.'

            messages.success(request, success_msg)
            return redirect('posts')

        except Exception as e:
            logger.error(f"Bulk upload error: {e}")
            messages.error(request, 'An error occurred during bulk upload')
            return redirect('bulk-upload')

    # GET request - show the form
    context = {
        'max_files': 10,  # Limit for bulk upload
        'max_file_size': '10MB',
        'supported_formats': ['JPG', 'PNG', 'GIF', 'WEBP']
    }

    return render(request, 'bulk_upload.html', context)


def advanced_scheduler(request):
    """Advanced scheduling interface with calendar integration."""
    if not request.user.is_authenticated:
        return redirect('login')

    from .models import ScheduledPost, PostAnalytics, UserAnalytics
    from datetime import datetime, timedelta

    # Get user analytics for optimal timing
    user_analytics, created = UserAnalytics.objects.get_or_create(user=request.user)

    # Get scheduled posts for the next 30 days
    start_date = timezone.now()
    end_date = start_date + timedelta(days=30)

    scheduled_posts = ScheduledPost.objects.filter(
        user=request.user,
        scheduled_for__range=[start_date, end_date]
    ).order_by('scheduled_for')

    # Calculate optimal posting times based on user's historical data
    post_analytics = PostAnalytics.objects.filter(post__user=request.user)

    # Group by hour and day of week to find patterns
    hourly_performance = {}
    daily_performance = {}

    for analytics in post_analytics:
        hour = analytics.post.created.hour
        day = analytics.post.created.weekday()  # 0=Monday, 6=Sunday

        if hour not in hourly_performance:
            hourly_performance[hour] = []
        hourly_performance[hour].append(analytics.engagement_rate)

        if day not in daily_performance:
            daily_performance[day] = []
        daily_performance[day].append(analytics.engagement_rate)

    # Calculate average engagement by hour and day
    optimal_hours = []
    for hour in range(24):
        if hour in hourly_performance:
            avg_engagement = sum(hourly_performance[hour]) / len(hourly_performance[hour])
            optimal_hours.append({'hour': hour, 'engagement': round(avg_engagement, 2)})

    optimal_hours.sort(key=lambda x: x['engagement'], reverse=True)

    # Default optimal times if no data
    if not optimal_hours:
        optimal_times = [
            {'day': 'Monday', 'time': '09:00', 'engagement': 85},
            {'day': 'Tuesday', 'time': '14:00', 'engagement': 92},
            {'day': 'Wednesday', 'time': '11:00', 'engagement': 88},
            {'day': 'Thursday', 'time': '15:00', 'engagement': 90},
            {'day': 'Friday', 'time': '13:00', 'engagement': 87},
            {'day': 'Saturday', 'time': '10:00', 'engagement': 83},
            {'day': 'Sunday', 'time': '16:00', 'engagement': 89},
        ]
    else:
        # Use calculated optimal times
        days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday']
        optimal_times = []
        for i, day in enumerate(days):
            best_hour = optimal_hours[i % len(optimal_hours)]['hour'] if optimal_hours else 12
            engagement = optimal_hours[i % len(optimal_hours)]['engagement'] if optimal_hours else 85
            optimal_times.append({
                'day': day,
                'time': f'{best_hour:02d}:00',
                'engagement': engagement
            })

    # Content suggestions based on day of week and user's performance
    content_suggestions = {
        'Monday': 'Motivational content, week planning, goals',
        'Tuesday': 'Educational content, tutorials, tips',
        'Wednesday': 'Behind-the-scenes, team highlights',
        'Thursday': 'Product features, announcements',
        'Friday': 'Fun content, team celebrations',
        'Saturday': 'Lifestyle content, personal stories',
        'Sunday': 'Inspirational quotes, week recap'
    }

    # Get pending posts that can be scheduled
    pending_posts = Post.objects.filter(
        user=request.user,
        posted=False
    ).exclude(
        schedule__isnull=False
    )[:10]  # Limit to 10 for performance

    # Calculate scheduling recommendations
    recommendations = []
    if pending_posts.exists():
        recommendations.append(f"You have {pending_posts.count()} posts ready to schedule.")

    if user_analytics.best_posting_hour:
        recommendations.append(f"Your best posting time is {user_analytics.best_posting_hour}:00.")

    if scheduled_posts.count() < 7:
        recommendations.append("Consider scheduling more posts to maintain consistent presence.")

    context = {
        'scheduled_posts': scheduled_posts,
        'pending_posts': pending_posts,
        'optimal_times': optimal_times,
        'content_suggestions': content_suggestions,
        'recommendations': recommendations,
        'current_month': timezone.now().strftime('%B %Y'),
        'user_analytics': user_analytics,
        'total_scheduled': scheduled_posts.count(),
        'total_pending': pending_posts.count()
    }

    return render(request, 'advanced_scheduler.html', context)


def create_template(request):
    """Create a new content template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ContentTemplate

    try:
        template = ContentTemplate.objects.create(
            user=request.user,
            name=request.POST.get('name'),
            template_type=request.POST.get('template_type'),
            content=request.POST.get('content'),
            industry=request.POST.get('industry', ''),
            tone=request.POST.get('tone', ''),
            hashtags=request.POST.get('hashtags', ''),
            is_public=request.POST.get('is_public') == 'true'
        )

        return JsonResponse({
            'success': True,
            'template_id': template.id,
            'message': f'Template "{template.name}" created successfully!'
        })

    except Exception as e:
        logger.error(f"Error creating template: {e}")
        return JsonResponse({'error': 'Failed to create template'}, status=500)


def delete_template(request, template_id):
    """Delete a content template."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'DELETE':
        return JsonResponse({'error': 'DELETE method required'}, status=405)

    from .models import ContentTemplate

    try:
        template = ContentTemplate.objects.get(id=template_id, user=request.user)
        template_name = template.name
        template.delete()

        return JsonResponse({
            'success': True,
            'message': f'Template "{template_name}" deleted successfully!'
        })

    except ContentTemplate.DoesNotExist:
        return JsonResponse({'error': 'Template not found'}, status=404)
    except Exception as e:
        logger.error(f"Error deleting template: {e}")
        return JsonResponse({'error': 'Failed to delete template'}, status=500)


def schedule_post(request):
    """Schedule a post for future publication."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    if request.method != 'POST':
        return JsonResponse({'error': 'POST method required'}, status=405)

    from .models import ScheduledPost
    from datetime import datetime

    try:
        post_id = request.POST.get('post_id')
        scheduled_for = request.POST.get('scheduled_for')
        platforms = request.POST.getlist('platforms')

        if not post_id or not scheduled_for:
            return JsonResponse({'error': 'Post ID and schedule time required'}, status=400)

        # Get the post
        post = Post.objects.get(id=post_id, user=request.user)

        # Parse the scheduled time
        scheduled_datetime = datetime.fromisoformat(scheduled_for.replace('Z', '+00:00'))

        # Create or update scheduled post
        scheduled_post, created = ScheduledPost.objects.get_or_create(
            post=post,
            defaults={
                'user': request.user,
                'scheduled_for': scheduled_datetime,
                'target_platforms': platforms or ['instagram']
            }
        )

        if not created:
            scheduled_post.scheduled_for = scheduled_datetime
            scheduled_post.target_platforms = platforms or ['instagram']
            scheduled_post.status = 'pending'
            scheduled_post.save()

        return JsonResponse({
            'success': True,
            'message': f'Post scheduled for {scheduled_datetime.strftime("%B %d, %Y at %I:%M %p")}'
        })

    except Post.DoesNotExist:
        return JsonResponse({'error': 'Post not found'}, status=404)
    except ValueError as e:
        return JsonResponse({'error': f'Invalid date format: {e}'}, status=400)
    except Exception as e:
        logger.error(f"Error scheduling post: {e}")
        return JsonResponse({'error': 'Failed to schedule post'}, status=500)


def get_analytics_data(request):
    """Get analytics data for charts and graphs."""
    if not request.user.is_authenticated:
        return JsonResponse({'error': 'Authentication required'}, status=401)

    from .models import PostAnalytics, UserAnalytics
    from datetime import datetime, timedelta

    try:
        days = int(request.GET.get('days', 30))
        start_date = timezone.now() - timedelta(days=days)

        # Get post analytics for the period
        analytics = PostAnalytics.objects.filter(
            post__user=request.user,
            post__created__gte=start_date
        ).order_by('post__created')

        # Prepare data for charts
        engagement_data = []
        reach_data = []
        dates = []

        for a in analytics:
            dates.append(a.post.created.strftime('%Y-%m-%d'))
            engagement_data.append(a.engagement_rate)
            reach_data.append(a.reach)

        # Get user analytics
        user_analytics = UserAnalytics.objects.filter(user=request.user).first()

        return JsonResponse({
            'success': True,
            'data': {
                'dates': dates,
                'engagement_rates': engagement_data,
                'reach': reach_data,
                'total_posts': len(analytics),
                'avg_engagement': user_analytics.avg_engagement_rate if user_analytics else 0,
                'total_likes': user_analytics.total_likes if user_analytics else 0,
                'total_comments': user_analytics.total_comments if user_analytics else 0
            }
        })

    except Exception as e:
        logger.error(f"Error getting analytics data: {e}")
        return JsonResponse({'error': 'Failed to get analytics data'}, status=500)