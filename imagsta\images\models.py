from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import URLValida<PERSON>, MinLengthValidator
from django.utils import timezone
import uuid


class User(AbstractUser):
    """Extended user model with additional fields for social media management."""
    pass


class Search(models.Model):
    """Model to store search queries and track search history."""
    query = models.CharField(
        max_length=128,
        unique=True,
        validators=[MinLengthValidator(2)],
        help_text="Search query for images"
    )
    search_count = models.PositiveIntegerField(default=1, help_text="Number of times this query was searched")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated']
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['-updated']),
        ]

    def __str__(self):
        return f"{self.query} ({self.search_count} searches)"

    def increment_search_count(self):
        """Increment the search count and update timestamp."""
        self.search_count += 1
        self.updated = timezone.now()
        self.save(update_fields=['search_count', 'updated'])


class Result(models.Model):
    """Model to store image search results from external APIs."""
    query = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results')
    image = models.URLField()
    dis_image = models.URLField(blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.image


class Post(models.Model):
    """Model to store user-created posts for social media."""
    user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='posts')
    image = models.ImageField(upload_to='images/posts')
    caption = models.TextField(blank=True)
    post_text = models.TextField(max_length=130, blank=True)
    hashtags = models.TextField(blank=True)
    posted = models.BooleanField(default=False)
    published_image_id = models.CharField(blank=True, max_length=200)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.pk} - {self.user}"


class ContentTemplate(models.Model):
    """Model for reusable content templates."""
    TEMPLATE_TYPES = [
        ('caption', 'Caption Template'),
        ('hashtag_set', 'Hashtag Set'),
        ('post_series', 'Post Series'),
        ('campaign', 'Campaign Template'),
    ]

    INDUSTRIES = [
        ('technology', 'Technology'),
        ('fashion', 'Fashion'),
        ('food', 'Food & Beverage'),
        ('travel', 'Travel'),
        ('fitness', 'Fitness & Health'),
        ('business', 'Business'),
        ('lifestyle', 'Lifestyle'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('other', 'Other'),
    ]

    TONES = [
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('humorous', 'Humorous'),
        ('inspirational', 'Inspirational'),
        ('educational', 'Educational'),
        ('promotional', 'Promotional'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content = models.TextField(help_text="Template content with placeholders like {product_name}")
    industry = models.CharField(max_length=50, choices=INDUSTRIES, blank=True)
    tone = models.CharField(max_length=20, choices=TONES, blank=True)
    hashtags = models.TextField(blank=True, help_text="Default hashtags for this template")
    usage_count = models.PositiveIntegerField(default=0)
    is_public = models.BooleanField(default=False, help_text="Make template available to other users")
    is_featured = models.BooleanField(default=False, help_text="Featured template")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    def render_content(self, variables=None):
        """Render template content with provided variables."""
        if not variables:
            return self.content

        try:
            return self.content.format(**variables)
        except KeyError as e:
            return f"Missing variable: {e}"


class SavedHashtagSet(models.Model):
    """Model for saving hashtag sets for reuse."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hashtag_sets')
    name = models.CharField(max_length=100)
    hashtags = models.TextField(help_text="Comma-separated hashtags")
    industry = models.CharField(max_length=50, blank=True)
    usage_count = models.PositiveIntegerField(default=0)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-usage_count', '-created']

    def __str__(self):
        return self.name

    def get_hashtags_list(self):
        """Return hashtags as a list."""
        return [tag.strip() for tag in self.hashtags.split(',') if tag.strip()]

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])


class PostAnalytics(models.Model):
    """Model to track post performance analytics."""
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='analytics')
    likes_count = models.PositiveIntegerField(default=0)
    comments_count = models.PositiveIntegerField(default=0)
    shares_count = models.PositiveIntegerField(default=0)
    reach = models.PositiveIntegerField(default=0)
    impressions = models.PositiveIntegerField(default=0)
    engagement_rate = models.FloatField(default=0.0)
    last_updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Post Analytics"

    def __str__(self):
        return f"Analytics for {self.post}"

    @property
    def total_engagement(self):
        """Calculate total engagement."""
        return self.likes_count + self.comments_count + self.shares_count

    def update_engagement_rate(self):
        """Calculate and update engagement rate."""
        if self.impressions > 0:
            self.engagement_rate = (self.total_engagement / self.impressions) * 100
        else:
            self.engagement_rate = 0.0
        self.save(update_fields=['engagement_rate'])


class UserAnalytics(models.Model):
    """Model to track user-level analytics."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='analytics')
    total_posts = models.PositiveIntegerField(default=0)
    total_likes = models.PositiveIntegerField(default=0)
    total_comments = models.PositiveIntegerField(default=0)
    total_followers = models.PositiveIntegerField(default=0)
    total_following = models.PositiveIntegerField(default=0)
    avg_engagement_rate = models.FloatField(default=0.0)
    best_posting_hour = models.PositiveIntegerField(default=12)  # 0-23
    best_posting_day = models.PositiveIntegerField(default=1)   # 1-7 (Monday-Sunday)
    last_updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "User Analytics"

    def __str__(self):
        return f"Analytics for {self.user.username}"

    def update_totals(self):
        """Update total counts from post analytics."""
        post_analytics = PostAnalytics.objects.filter(post__user=self.user)
        self.total_posts = self.user.posts.count()
        self.total_likes = sum(pa.likes_count for pa in post_analytics)
        self.total_comments = sum(pa.comments_count for pa in post_analytics)

        # Calculate average engagement rate
        if post_analytics.exists():
            self.avg_engagement_rate = sum(pa.engagement_rate for pa in post_analytics) / post_analytics.count()
        else:
            self.avg_engagement_rate = 0.0

        self.save()


class ContentTemplate(models.Model):
    """Model for reusable content templates."""
    TEMPLATE_TYPES = [
        ('caption', 'Caption Template'),
        ('hashtag_set', 'Hashtag Set'),
        ('post_series', 'Post Series'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content = models.TextField()
    industry = models.CharField(max_length=50, blank=True)
    tone = models.CharField(max_length=20, blank=True)
    usage_count = models.PositiveIntegerField(default=0)
    is_public = models.BooleanField(default=False)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])