from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import URL<PERSON><PERSON><PERSON><PERSON>, MinLengthValidator
from django.utils import timezone
import uuid


class User(AbstractUser):
    """Extended user model with additional fields for social media management."""
    email = models.EmailField(unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'auth_user'
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
        ]


class Search(models.Model):
    """Model to store search queries and track search history."""
    query = models.CharField(
        max_length=128,
        unique=True,
        validators=[MinLengthValidator(2)],
        help_text="Search query for images"
    )
    search_count = models.PositiveIntegerField(default=1, help_text="Number of times this query was searched")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTime<PERSON>ield(auto_now=True)

    class Meta:
        ordering = ['-updated']
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['-updated']),
        ]

    def __str__(self):
        return f"{self.query} ({self.search_count} searches)"

    def increment_search_count(self):
        """Increment the search count and update timestamp."""
        self.search_count += 1
        self.updated = timezone.now()
        self.save(update_fields=['search_count', 'updated'])


class Result(models.Model):
    """Model to store image search results from external APIs."""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    query = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results')
    image = models.URLField(validators=[URLValidator()], help_text="Full resolution image URL")
    dis_image = models.URLField(
        blank=True,
        validators=[URLValidator()],
        help_text="Display/thumbnail image URL"
    )
    image_alt = models.CharField(max_length=255, blank=True, help_text="Alt text for the image")
    source = models.CharField(max_length=50, default='unsplash', help_text="Source of the image")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created']
        indexes = [
            models.Index(fields=['query', '-created']),
            models.Index(fields=['source']),
        ]

    def __str__(self):
        return f"Image for '{self.query.query}' from {self.source}"


class Post(models.Model):
    """Model to store user-created posts for social media."""

    STATUS_CHOICES = [
        ('draft', 'Draft'),
        ('ready', 'Ready to Post'),
        ('posted', 'Posted'),
        ('failed', 'Failed to Post'),
    ]

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='posts')
    image = models.ImageField(upload_to='images/posts/%Y/%m/%d/')
    caption = models.TextField(blank=True, help_text="Caption for the post")
    post_text = models.TextField(
        max_length=2200,  # Instagram caption limit
        blank=True,
        help_text="Text overlay on the image"
    )
    hashtags = models.TextField(blank=True, help_text="Hashtags for the post")
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default='draft')
    published_image_id = models.CharField(blank=True, max_length=200, help_text="Instagram post ID")
    scheduled_for = models.DateTimeField(null=True, blank=True, help_text="Schedule post for later")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-created']
        indexes = [
            models.Index(fields=['user', '-created']),
            models.Index(fields=['status']),
            models.Index(fields=['scheduled_for']),
        ]

    def __str__(self):
        return f"Post {self.id} by {self.user.username} ({self.status})"

    @property
    def is_posted(self):
        """Check if the post has been successfully posted."""
        return self.status == 'posted' and bool(self.published_image_id)

    def mark_as_posted(self, instagram_id):
        """Mark the post as successfully posted."""
        self.status = 'posted'
        self.published_image_id = instagram_id
        self.save(update_fields=['status', 'published_image_id', 'updated'])

    def mark_as_failed(self):
        """Mark the post as failed to post."""
        self.status = 'failed'
        self.save(update_fields=['status', 'updated'])