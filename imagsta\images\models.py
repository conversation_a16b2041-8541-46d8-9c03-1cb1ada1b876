from django.db import models
from django.contrib.auth.models import AbstractUser
from django.core.validators import URLValida<PERSON>, MinLengthValidator
from django.utils import timezone
import uuid


class User(AbstractUser):
    """Extended user model with additional fields for social media management."""
    pass


class Search(models.Model):
    """Model to store search queries and track search history."""
    query = models.CharField(
        max_length=128,
        unique=True,
        validators=[MinLengthValidator(2)],
        help_text="Search query for images"
    )
    search_count = models.PositiveIntegerField(default=1, help_text="Number of times this query was searched")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-updated']
        indexes = [
            models.Index(fields=['query']),
            models.Index(fields=['-updated']),
        ]

    def __str__(self):
        return f"{self.query} ({self.search_count} searches)"

    def increment_search_count(self):
        """Increment the search count and update timestamp."""
        self.search_count += 1
        self.updated = timezone.now()
        self.save(update_fields=['search_count', 'updated'])


class Result(models.Model):
    """Model to store image search results from external APIs."""
    query = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results')
    image = models.URLField()
    dis_image = models.URLField(blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.image


class Post(models.Model):
    """Model to store user-created posts for social media."""
    user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='posts')
    image = models.ImageField(upload_to='images/posts')
    caption = models.TextField(blank=True)
    post_text = models.TextField(max_length=130, blank=True)
    hashtags = models.TextField(blank=True)
    posted = models.BooleanField(default=False)
    published_image_id = models.CharField(blank=True, max_length=200)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.pk} - {self.user}"


class PostAnalytics(models.Model):
    """Model to track post performance analytics."""
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='analytics')
    likes_count = models.PositiveIntegerField(default=0)
    comments_count = models.PositiveIntegerField(default=0)
    shares_count = models.PositiveIntegerField(default=0)
    saves_count = models.PositiveIntegerField(default=0)
    reach = models.PositiveIntegerField(default=0)
    impressions = models.PositiveIntegerField(default=0)
    engagement_rate = models.FloatField(default=0.0)
    click_through_rate = models.FloatField(default=0.0)

    # Platform-specific metrics
    platform_data = models.JSONField(default=dict, blank=True)

    # Tracking timestamps
    last_updated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "Post Analytics"
        indexes = [
            models.Index(fields=['post', '-last_updated']),
            models.Index(fields=['engagement_rate']),
        ]

    def __str__(self):
        return f"Analytics for {self.post}"

    @property
    def total_engagement(self):
        """Calculate total engagement."""
        return self.likes_count + self.comments_count + self.shares_count + self.saves_count

    def update_engagement_rate(self):
        """Calculate and update engagement rate."""
        if self.impressions > 0:
            self.engagement_rate = (self.total_engagement / self.impressions) * 100
        else:
            self.engagement_rate = 0.0
        self.save(update_fields=['engagement_rate'])

    def get_performance_score(self):
        """Calculate overall performance score (0-100)."""
        # Weighted scoring based on different metrics
        engagement_weight = 0.4
        reach_weight = 0.3
        ctr_weight = 0.3

        # Normalize metrics (assuming benchmarks)
        engagement_score = min(self.engagement_rate * 10, 100)  # 10% engagement = 100 score
        reach_score = min((self.reach / max(self.impressions, 1)) * 100, 100)
        ctr_score = min(self.click_through_rate * 20, 100)  # 5% CTR = 100 score

        return (engagement_score * engagement_weight +
                reach_score * reach_weight +
                ctr_score * ctr_weight)


class UserAnalytics(models.Model):
    """Model to track user-level analytics and insights."""
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='analytics')

    # Aggregate metrics
    total_posts = models.PositiveIntegerField(default=0)
    total_likes = models.PositiveIntegerField(default=0)
    total_comments = models.PositiveIntegerField(default=0)
    total_shares = models.PositiveIntegerField(default=0)
    total_followers = models.PositiveIntegerField(default=0)
    total_following = models.PositiveIntegerField(default=0)

    # Calculated metrics
    avg_engagement_rate = models.FloatField(default=0.0)
    avg_likes_per_post = models.FloatField(default=0.0)
    avg_comments_per_post = models.FloatField(default=0.0)

    # Optimal timing data
    best_posting_hour = models.PositiveIntegerField(default=12)  # 0-23
    best_posting_day = models.PositiveIntegerField(default=1)   # 1-7 (Monday-Sunday)

    # Growth metrics
    follower_growth_rate = models.FloatField(default=0.0)  # Monthly growth rate
    engagement_growth_rate = models.FloatField(default=0.0)

    # Content insights
    top_performing_hashtags = models.JSONField(default=list, blank=True)
    content_type_performance = models.JSONField(default=dict, blank=True)

    # Timestamps
    last_calculated = models.DateTimeField(auto_now=True)
    created = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name_plural = "User Analytics"

    def __str__(self):
        return f"Analytics for {self.user.username}"

    def update_totals(self):
        """Update total counts from post analytics."""
        post_analytics = PostAnalytics.objects.filter(post__user=self.user)

        self.total_posts = self.user.posts.count()
        self.total_likes = sum(pa.likes_count for pa in post_analytics)
        self.total_comments = sum(pa.comments_count for pa in post_analytics)
        self.total_shares = sum(pa.shares_count for pa in post_analytics)

        # Calculate averages
        if self.total_posts > 0:
            self.avg_likes_per_post = self.total_likes / self.total_posts
            self.avg_comments_per_post = self.total_comments / self.total_posts

        # Calculate average engagement rate
        if post_analytics.exists():
            self.avg_engagement_rate = sum(pa.engagement_rate for pa in post_analytics) / post_analytics.count()
        else:
            self.avg_engagement_rate = 0.0

        self.save()

    def get_top_posts(self, limit=5):
        """Get top performing posts."""
        return PostAnalytics.objects.filter(
            post__user=self.user
        ).order_by('-engagement_rate')[:limit]

    def get_engagement_trend(self, days=30):
        """Get engagement trend over specified days."""
        from datetime import datetime, timedelta

        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        analytics = PostAnalytics.objects.filter(
            post__user=self.user,
            post__created__range=[start_date, end_date]
        ).order_by('post__created')

        return [
            {
                'date': a.post.created.strftime('%Y-%m-%d'),
                'engagement_rate': a.engagement_rate,
                'total_engagement': a.total_engagement
            }
            for a in analytics
        ]


class ContentTemplate(models.Model):
    """Model for reusable content templates."""
    TEMPLATE_TYPES = [
        ('caption', 'Caption Template'),
        ('hashtag_set', 'Hashtag Set'),
        ('post_series', 'Post Series'),
        ('campaign', 'Campaign Template'),
    ]

    INDUSTRIES = [
        ('technology', 'Technology'),
        ('fashion', 'Fashion'),
        ('food', 'Food & Beverage'),
        ('travel', 'Travel'),
        ('fitness', 'Fitness & Health'),
        ('business', 'Business'),
        ('lifestyle', 'Lifestyle'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('other', 'Other'),
    ]

    TONES = [
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('humorous', 'Humorous'),
        ('inspirational', 'Inspirational'),
        ('educational', 'Educational'),
        ('promotional', 'Promotional'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content = models.TextField(help_text="Template content with placeholders like {product_name}")
    industry = models.CharField(max_length=50, choices=INDUSTRIES, blank=True)
    tone = models.CharField(max_length=20, choices=TONES, blank=True)
    hashtags = models.TextField(blank=True, help_text="Default hashtags for this template")

    # Template metadata
    variables = models.JSONField(default=list, blank=True, help_text="List of variables used in template")
    usage_count = models.PositiveIntegerField(default=0)
    is_public = models.BooleanField(default=False, help_text="Make template available to other users")
    is_featured = models.BooleanField(default=False, help_text="Featured template")

    # Performance tracking
    avg_engagement_rate = models.FloatField(default=0.0)
    total_uses = models.PositiveIntegerField(default=0)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']
        indexes = [
            models.Index(fields=['user', 'template_type']),
            models.Index(fields=['industry', 'tone']),
            models.Index(fields=['is_public', 'is_featured']),
        ]

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.total_uses += 1
        self.save(update_fields=['usage_count', 'total_uses'])

    def render_content(self, variables=None):
        """Render template content with provided variables."""
        if not variables:
            return self.content

        try:
            return self.content.format(**variables)
        except KeyError as e:
            return f"Missing variable: {e}"

    def extract_variables(self):
        """Extract variable names from template content."""
        import re
        variables = re.findall(r'\{(\w+)\}', self.content)
        return list(set(variables))

    def save(self, *args, **kwargs):
        """Override save to auto-extract variables."""
        self.variables = self.extract_variables()
        super().save(*args, **kwargs)


class SavedHashtagSet(models.Model):
    """Model for saving hashtag sets for reuse."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='hashtag_sets')
    name = models.CharField(max_length=100)
    hashtags = models.TextField(help_text="Comma-separated hashtags")
    industry = models.CharField(max_length=50, blank=True)
    description = models.TextField(blank=True)

    # Performance metrics
    usage_count = models.PositiveIntegerField(default=0)
    avg_engagement_rate = models.FloatField(default=0.0)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']
        indexes = [
            models.Index(fields=['user', 'industry']),
        ]

    def __str__(self):
        return self.name

    def get_hashtags_list(self):
        """Return hashtags as a list."""
        return [tag.strip() for tag in self.hashtags.split(',') if tag.strip()]

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    @property
    def hashtag_count(self):
        """Return number of hashtags in this set."""
        return len(self.get_hashtags_list())


class ScheduledPost(models.Model):
    """Model for scheduling posts for future publication."""
    STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('posted', 'Posted'),
        ('failed', 'Failed'),
        ('cancelled', 'Cancelled'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='scheduled_posts')
    post = models.OneToOneField(Post, on_delete=models.CASCADE, related_name='schedule')

    # Scheduling details
    scheduled_for = models.DateTimeField()
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Platform targeting
    target_platforms = models.JSONField(default=list, help_text="List of platforms to post to")

    # Retry logic
    retry_count = models.PositiveIntegerField(default=0)
    max_retries = models.PositiveIntegerField(default=3)
    last_attempt = models.DateTimeField(null=True, blank=True)
    error_message = models.TextField(blank=True)

    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['scheduled_for']
        indexes = [
            models.Index(fields=['user', 'status', 'scheduled_for']),
            models.Index(fields=['status', 'scheduled_for']),
        ]

    def __str__(self):
        return f"Scheduled post for {self.scheduled_for}"

    def can_retry(self):
        """Check if post can be retried."""
        return self.status == 'failed' and self.retry_count < self.max_retries

    def mark_as_processing(self):
        """Mark post as being processed."""
        self.status = 'processing'
        self.save(update_fields=['status'])

    def mark_as_posted(self):
        """Mark post as successfully posted."""
        self.status = 'posted'
        self.save(update_fields=['status'])

    def mark_as_failed(self, error_message=''):
        """Mark post as failed with optional error message."""
        self.status = 'failed'
        self.retry_count += 1
        self.error_message = error_message
        self.last_attempt = timezone.now()
        self.save(update_fields=['status', 'retry_count', 'error_message', 'last_attempt'])


class ContentTemplate(models.Model):
    """Model for reusable content templates."""
    TEMPLATE_TYPES = [
        ('caption', 'Caption Template'),
        ('hashtag_set', 'Hashtag Set'),
        ('post_series', 'Post Series'),
        ('campaign', 'Campaign Template'),
    ]

    INDUSTRIES = [
        ('technology', 'Technology'),
        ('fashion', 'Fashion'),
        ('food', 'Food & Beverage'),
        ('travel', 'Travel'),
        ('fitness', 'Fitness & Health'),
        ('business', 'Business'),
        ('lifestyle', 'Lifestyle'),
        ('education', 'Education'),
        ('entertainment', 'Entertainment'),
        ('other', 'Other'),
    ]

    TONES = [
        ('professional', 'Professional'),
        ('casual', 'Casual'),
        ('humorous', 'Humorous'),
        ('inspirational', 'Inspirational'),
        ('educational', 'Educational'),
        ('promotional', 'Promotional'),
    ]

    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='templates')
    name = models.CharField(max_length=100)
    template_type = models.CharField(max_length=20, choices=TEMPLATE_TYPES)
    content = models.TextField(help_text="Template content with placeholders like {product_name}")
    industry = models.CharField(max_length=50, choices=INDUSTRIES, blank=True)
    tone = models.CharField(max_length=20, choices=TONES, blank=True)
    hashtags = models.TextField(blank=True, help_text="Default hashtags for this template")
    usage_count = models.PositiveIntegerField(default=0)
    is_public = models.BooleanField(default=False, help_text="Make template available to other users")
    is_featured = models.BooleanField(default=False, help_text="Featured template")
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-usage_count', '-created']

    def __str__(self):
        return f"{self.name} ({self.template_type})"

    def increment_usage(self):
        """Increment usage count."""
        self.usage_count += 1
        self.save(update_fields=['usage_count'])

    def render_content(self, variables=None):
        """Render template content with provided variables."""
        if not variables:
            return self.content

        try:
            return self.content.format(**variables)
        except KeyError as e:
            return f"Missing variable: {e}"


