from django.db import models
from django.contrib.auth.models import AbstractUser

class User(AbstractUser):
    pass

class Search(models.Model):
    query = models.CharField(max_length=128, unique=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.query

class Result(models.Model):
    query = models.ForeignKey(Search, on_delete=models.CASCADE, related_name='results')
    image = models.URLField()
    dis_image = models.URLField(blank=True)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.image


class Post(models.Model):
    user = models.ForeignKey(User, on_delete=models.PROTECT, related_name='posts')
    image = models.ImageField(upload_to='images/posts')
    caption = models.TextField(blank=True)
    post_text = models.TextField(max_length=130, blank=True)
    hashtags = models.TextField(blank=True)
    posted = models.BooleanField(default=False)
    published_image_id = models.CharField(blank=True, max_length=200)
    created = models.DateTimeField(auto_now_add=True)
    updated = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.pk} - {self.user}"