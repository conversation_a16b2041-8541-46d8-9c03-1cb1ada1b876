{% extends 'base.html' %}
{% load static %}
{% block content %}

{% if user.is_authenticated %}
<div class="px-4 my-5 text-center">
  <img class="d-block mx-auto mb-4 rounded" src="{% static 'images/logoo.png' %}" alt="" width="72" height="57">
  <h1 class="display-5 fw-bold mb-4 text-light">Imgsta</h1>
  <div class="col-lg-6 mx-auto">
    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
      <a type="button" href="{% url 'create-custom' %}" class="btn btn-danger px-5 py-3 gap-3">Create custom</a>
      <a type="button" href="{% url 'get-feed' %}" class="btn btn-outline-secondary btn-lg my-auto fw-bold p-3">Loud</a>
    </div>

  </div>
</div>

<div class="row container-fluid mx-auto">
    <div class="col ">
        
        <h2 class="h2 mt-4 text-light fw-bolder p-5 text-center mx-auto">Business</h2>
        <ul class="list-group">
            {% for headline in b_headlines.entries %}
                
                    
                    <li  id="news_item" class="p-3 list-group-item mt-2 list-group-item-action bg-dark text-light"><span>{{forloop.counter}}</span><p>{{headline.title}} </p>
                      <form action="{% url 'new-feed' %}" method="post">  
                        {% csrf_token %}
                        <input type="hidden" name="title" id="" value="{{headline.title}}">
                        <spam class="text-danger ">{{headline.published}}</spam> 
                        <button type="submit"class="btn btn-sm btn-danger p-2 float-end">Select</button>
                      </form>
                    </li>
                    
            {% endfor %}
        </ul>
    </div>
    <div class="col">
        <h2 id="tech" class="h2 mt-4 text-light fw-bolder p-5 text-center mx-auto">Technology</h2>
        <ul class="list-group">
            {% for headline in t_headlines.entries %}
                
            <li  id="news_item" class="p-3 list-group-item mt-2 list-group-item-action bg-dark text-light"><span>{{forloop.counter}}</span><p>{{headline.title}} </p>
              <form action="{% url 'new-feed' %}" method="post">  
                {% csrf_token %}
                <input type="hidden" name="title" id="" value="{{headline.title}}">
                <spam class="text-danger ">{{headline.published}}</spam> 
                <button type="submit"class="btn btn-sm btn-danger p-2 float-end">Select</button>
              </form>
            </li>
                    
            {% endfor %}
        </ul>
    </div>
    <div class="col">
        
        <h2 class="h2 mt-4 text-light fw-bolder p-5 text-center mx-auto">World</h2>
        <ul class="list-group">
            {% for headline in w_headlines.entries %}
                
                  <li  id="news_item" class="p-3 list-group-item mt-2 list-group-item-action bg-dark text-light"><span>{{forloop.counter}}</span><p>{{headline.title}} </p>
                    <form action="{% url 'new-feed' %}" method="post">  
                      {% csrf_token %}
                      <input type="hidden" name="title" id="" value="{{headline.title}}">
                      <spam class="text-danger ">{{headline.published}}</spam> 
                      <button type="submit"class="btn btn-sm btn-danger p-2 float-end">Select</button>
                    </form>
                  </li>         
            {% endfor %}
        </ul>
    </div>
    
</div>
{% else %}

<div class="px-4 my-5 text-center">
  <img class="d-block mx-auto mb-4 rounded" src="{% static 'images/logoo.png' %}" alt="" width="72" height="57">
  <h1 class="display-5 fw-bold mb-4 text-light">Imgsta</h1>
  <div class="col-lg-6 mx-auto">
    <div class="d-grid gap-2 d-sm-flex justify-content-sm-center">
      <a type="button" href="{% url 'login' %}" class="btn btn-danger btn-lg my-auto fw-bold p-3">Login</a>
    </div>

  </div>
</div>

{% endif %}
{% endblock content %}