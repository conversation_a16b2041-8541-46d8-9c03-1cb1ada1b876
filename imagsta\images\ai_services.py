"""
AI-powered content generation services for Imagsta.
This module provides intelligent content creation capabilities including
caption generation, hashtag suggestions, and content optimization.
"""

import logging
import requests
import json
from typing import List, Dict, Optional
from django.conf import settings
from decouple import config

logger = logging.getLogger(__name__)


class AIContentService:
    """Service for AI-powered content generation and optimization."""
    
    def __init__(self):
        self.openai_api_key = config('OPENAI_API_KEY', default='')
        self.openai_base_url = 'https://api.openai.com/v1'
        
    def generate_caption(self, 
                        image_description: str, 
                        news_context: str, 
                        tone: str = 'professional',
                        max_length: int = 2200) -> Optional[str]:
        """
        Generate an engaging caption using AI based on image and news context.
        
        Args:
            image_description: Description of the image content
            news_context: Related news article or context
            tone: Desired tone (professional, casual, humorous, inspirational)
            max_length: Maximum caption length (Instagram limit is 2200)
            
        Returns:
            Generated caption or None if generation fails
        """
        if not self.openai_api_key:
            logger.warning("OpenAI API key not configured")
            return self._generate_fallback_caption(news_context, tone)
        
        try:
            prompt = self._build_caption_prompt(image_description, news_context, tone)
            
            response = requests.post(
                f"{self.openai_base_url}/chat/completions",
                headers={
                    'Authorization': f'Bearer {self.openai_api_key}',
                    'Content-Type': 'application/json'
                },
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [
                        {'role': 'system', 'content': 'You are a social media expert who creates engaging Instagram captions.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': min(max_length // 4, 500),  # Rough token estimation
                    'temperature': 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                caption = data['choices'][0]['message']['content'].strip()
                return caption[:max_length]  # Ensure length limit
            else:
                logger.error(f"OpenAI API error: {response.status_code} - {response.text}")
                return self._generate_fallback_caption(news_context, tone)
                
        except Exception as e:
            logger.error(f"Error generating caption: {e}")
            return self._generate_fallback_caption(news_context, tone)
    
    def suggest_hashtags(self, 
                        content: str, 
                        industry: str = 'general',
                        max_hashtags: int = 30) -> List[str]:
        """
        Generate relevant hashtags for the content.
        
        Args:
            content: The post content/caption
            industry: Industry or niche (tech, business, lifestyle, etc.)
            max_hashtags: Maximum number of hashtags to return
            
        Returns:
            List of suggested hashtags
        """
        if not self.openai_api_key:
            return self._generate_fallback_hashtags(content, industry)
        
        try:
            prompt = f"""
            Generate relevant Instagram hashtags for this content:
            
            Content: {content[:500]}
            Industry: {industry}
            
            Requirements:
            - Return {max_hashtags} hashtags
            - Mix of popular and niche hashtags
            - Include industry-specific tags
            - Format as comma-separated list without # symbol
            - No explanations, just the hashtags
            """
            
            response = requests.post(
                f"{self.openai_base_url}/chat/completions",
                headers={
                    'Authorization': f'Bearer {self.openai_api_key}',
                    'Content-Type': 'application/json'
                },
                json={
                    'model': 'gpt-3.5-turbo',
                    'messages': [
                        {'role': 'system', 'content': 'You are a social media hashtag expert.'},
                        {'role': 'user', 'content': prompt}
                    ],
                    'max_tokens': 200,
                    'temperature': 0.5
                },
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                hashtags_text = data['choices'][0]['message']['content'].strip()
                hashtags = [f"#{tag.strip()}" for tag in hashtags_text.split(',') if tag.strip()]
                return hashtags[:max_hashtags]
            else:
                logger.error(f"OpenAI API error for hashtags: {response.status_code}")
                return self._generate_fallback_hashtags(content, industry)
                
        except Exception as e:
            logger.error(f"Error generating hashtags: {e}")
            return self._generate_fallback_hashtags(content, industry)
    
    def optimize_posting_time(self, user_analytics: Dict) -> Dict[str, str]:
        """
        Suggest optimal posting times based on user analytics.
        
        Args:
            user_analytics: Dictionary containing user engagement data
            
        Returns:
            Dictionary with suggested posting times
        """
        # This would integrate with actual analytics data
        # For now, return general best practices
        return {
            'best_day': 'Tuesday',
            'best_time': '11:00 AM',
            'alternative_times': ['2:00 PM', '5:00 PM', '8:00 PM'],
            'reasoning': 'Based on general engagement patterns for your audience'
        }
    
    def analyze_content_performance(self, content: str) -> Dict[str, any]:
        """
        Predict content performance using AI analysis.
        
        Args:
            content: The content to analyze
            
        Returns:
            Dictionary with performance predictions
        """
        # Placeholder for ML model that would predict engagement
        return {
            'engagement_score': 7.5,  # Out of 10
            'predicted_likes': '150-300',
            'predicted_comments': '10-25',
            'virality_potential': 'Medium',
            'suggestions': [
                'Consider adding more emojis for better engagement',
                'The caption length is optimal for Instagram',
                'Adding a call-to-action could increase comments'
            ]
        }
    
    def _build_caption_prompt(self, image_description: str, news_context: str, tone: str) -> str:
        """Build the prompt for caption generation."""
        tone_instructions = {
            'professional': 'Use professional, informative language suitable for business audiences.',
            'casual': 'Use friendly, conversational language that feels natural and approachable.',
            'humorous': 'Add humor and wit while keeping it appropriate and engaging.',
            'inspirational': 'Use motivational and uplifting language that inspires action.'
        }
        
        return f"""
        Create an engaging Instagram caption based on:
        
        Image: {image_description}
        News Context: {news_context}
        Tone: {tone_instructions.get(tone, tone_instructions['professional'])}
        
        Requirements:
        - 50-150 words
        - Include relevant emojis
        - End with a call-to-action question
        - Make it engaging and shareable
        - Don't include hashtags (they'll be added separately)
        """
    
    def _generate_fallback_caption(self, news_context: str, tone: str) -> str:
        """Generate a simple fallback caption when AI is unavailable."""
        templates = {
            'professional': f"Stay informed about the latest developments. {news_context[:100]}... What are your thoughts on this?",
            'casual': f"Just saw this interesting news! {news_context[:100]}... What do you think?",
            'humorous': f"Well, this is interesting! 😄 {news_context[:100]}... Anyone else find this amusing?",
            'inspirational': f"Every challenge brings opportunity. {news_context[:100]}... How will you turn this into success?"
        }
        return templates.get(tone, templates['professional'])
    
    def _generate_fallback_hashtags(self, content: str, industry: str) -> List[str]:
        """Generate basic hashtags when AI is unavailable."""
        base_hashtags = ['#news', '#trending', '#update', '#socialmedia', '#content']
        
        industry_hashtags = {
            'tech': ['#technology', '#innovation', '#startup', '#digital', '#ai'],
            'business': ['#business', '#entrepreneur', '#marketing', '#growth', '#success'],
            'lifestyle': ['#lifestyle', '#inspiration', '#motivation', '#wellness', '#life'],
            'general': ['#daily', '#thoughts', '#share', '#community', '#discussion']
        }
        
        return base_hashtags + industry_hashtags.get(industry, industry_hashtags['general'])


# Singleton instance
ai_service = AIContentService()
