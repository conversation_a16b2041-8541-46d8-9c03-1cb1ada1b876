import json
import os
import textwrap
import time
import urllib
import urllib.request
import requests
from PIL import Image, ImageDraw, ImageFont
from bs4 import BeautifulSoup
from googleapiclient.discovery import build
from selenium import webdriver
from selenium.webdriver.firefox.options import Options   
from instabot import Bo<PERSON>
from django.templatetags.static import static
import pyimgur
import time



def download(urls):
    if not os.path.exists("downloaded"):
        os.makedirs("downloaded")
    for f in os.listdir("downloaded"):
        os.remove(os.path.join("downloaded", f))

    pic_num = 1

    for url in urls:

        try:
            print('Downloading => pic {}, {}\n'.format(pic_num, url))
            urllib.request.urlretrieve(url, "downloaded" + '/' + str(pic_num) + '.PNG')
            pic_num += 1
            time.sleep(1.5)

        except TypeError:
            print("image was not compatible to be downloaded")
            pass
        except urllib.error.HTTPError as e:
            print(e)
            pass


# Add the post's logo, text and shadow
def make_post(image, text):
    width, height = (1080, 1080)
    font_size = 54
    image = crop_max_square(image).resize((width, width), Image.LANCZOS)
    
    fade = Image.open('/home/<USER>/programming/imagsta/static/images/fade.png')
    rec = Image.open('/home/<USER>/programming/imagsta/static/images/V1.png')
    size = (150, 150)
    # image.paste(fade, fade)
    image.paste(fade, fade)
    image.paste(rec, rec)
    height = 880
    if len(text) > 37:
        height = 820
        font_size = 73
    if len(text) > 74:
        height = 810
        font_size = 71
    if len(text) >= 111:
        height = 790
        font_size = 70
    if len(text) >= 115:
        height = 785
        font_size = 65
        
    
    font = ImageFont.truetype("static/fonts/Dongle-Bold.ttf", font_size )
    draw_multiple_line_text(image, text, font, height)
    return image





# draws multiple lines text into an image
def draw_multiple_line_text(image, text, font, text_start_height):
    draw = ImageDraw.Draw(image)
    image_width, image_height = image.size
    y_text = text_start_height
    lines = textwrap.wrap(text, width=38)
    for line in lines:
        line_width, line_height = font.getsize(line) 
        draw.text(((image_width - line_width) / 2, y_text),
                  line, font=font, fill="white", stroke_fill="black", stroke_width=4)
        y_text += line_height - 12 


# crops the biggest square in an image
def crop_max_square(pil_img):
    return crop_square(pil_img, min(pil_img.size), min(pil_img.size))


# crops a square from the center of an image
def crop_square(pil_img, width, height):
    img_width, img_height = pil_img.size
    return pil_img.crop(((img_width - width) // 2,
                         (img_height - height) // 2,
                         (img_width + width) // 2,
                         (img_height + height) // 2))


# EAAibYPEaHzEBABImE2uuqdLudhCVkkWGMWAnX5n8XWc250LPZB6HOC97PqTQgRcLXcOnx9FZAWh35GCZAGGbdpGHWf10MSZA3lPDe94xZAlZAPEM2U8OZAcrxtZATzJFwnIl1pCKFxhRF1paPJlTKCjHpRH19tZBqZAwFZCHpVBveZA7XU6vDd1lF6W1PrbH1aZAb6BVDigML9d0NFXM613aA4qVd


def image_to_url(image):
    CLIENT_ID = "06e32a5edc3e2de"
    path = '/home/<USER>/programming/imagsta'+image
    im = pyimgur.Imgur(CLIENT_ID)
    uploaded_image = im.upload_image(path)
    url = uploaded_image.link
    return url


def getCreds() :
    """ Get creds required for use in the applications

    Returns:
        dictonary: credentials needed globally

    """

    creds = dict() # dictionary to hold everything
    creds['access_token'] = 'EAAibYPEaHzEBAFFMwYH5dTa7bCqPdZCDqvoAqUsCTuSSrl6R2zrqIdSChTHnqdoEkYeX27dgVK4V4nZCOnh3tBl3ntBfb1ZBIzHwzZA661DCBj1wlzDFyravIY14ktnrjCpcxZA6zZA2LE8i2jUbh5ZAttH1TZALsvEObFUFJGXcbCuyVBPFZBzKD' # access token for use with all api calls
    creds['graph_domain'] = 'https://graph.facebook.com/' # base domain for api calls
    creds['graph_version'] = 'v12.0' # version of the api we are hitting
    creds['endpoint_base'] = creds['graph_domain'] + creds['graph_version'] + '/' # base endpoint with domain and version
    creds['instagram_account_id'] = '*****************' # users instagram account id

    return creds

def makeApiCall( url, endpointParams, type ) :
    """ Request data from endpoint with params

    Args:
        url: string of the url endpoint to make request from
        endpointParams: dictionary keyed by the names of the url parameters


    Returns:
        object: data from the endpoint

    """

    if type == 'POST' : # post request
        data = requests.post( url, data=endpointParams )
    else : # get request
        data = requests.get( url, data=endpointParams )

    response = dict() # hold response info
    response['url'] = url # url we are hitting
    response['endpoint_params'] = endpointParams #parameters for the endpoint
    response['endpoint_params_pretty'] = json.dumps( endpointParams, indent = 4 ) # pretty print for cli
    response['json_data'] = json.loads( data.content ) # response data from the api
    response['json_data_pretty'] = json.dumps( response['json_data'], indent = 4 ) # pretty print for cli

    return response # get and return content


def createMediaObject( params ) :
	""" Create media object

	Args:
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/media?image_url={image-url}&caption={caption}&access_token={access-token}
		https://graph.facebook.com/v5.0/{ig-user-id}/media?video_url={video-url}&caption={caption}&access_token={access-token}

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/media' # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['access_token'] = params['access_token'] # access token
	endpointParams['caption'] = params['caption']  # caption for the post

	if 'IMAGE' == params['media_type'] : # posting image
		endpointParams['image_url'] = params['media_url']  # url to the asset
	else : # posting video
		endpointParams['media_type'] = params['media_type']  # specify media type
		endpointParams['video_url'] = params['media_url']  # url to the asset
	
	return makeApiCall( url, endpointParams, 'POST' ) # make the api call

def getMediaObjectStatus( mediaObjectId, params ) :
	""" Check the status of a media object

	Args:
		mediaObjectId: id of the media object
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-container-id}?fields=status_code

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + '/' + mediaObjectId # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['fields'] = 'status_code' # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'GET' ) # make the api call

def publishMedia( mediaObjectId, params ) :
	""" Publish content

	Args:
		mediaObjectId: id of the media object
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/media_publish?creation_id={creation-id}&access_token={access-token}

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/media_publish' # endpoint url
    
	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['creation_id'] = mediaObjectId # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'POST' ) # make the api call

def getContentPublishingLimit( params ) :
	""" Get the api limit for the user

	Args:
		params: dictionary of params
	
	API Endpoint:
		https://graph.facebook.com/v5.0/{ig-user-id}/content_publishing_limit?fields=config,quota_usage

	Returns:
		object: data from the endpoint

	"""

	url = params['endpoint_base'] + params['instagram_account_id'] + '/content_publishing_limit' # endpoint url

	endpointParams = dict() # parameter to send to the endpoint
	endpointParams['fields'] = 'config,quota_usage' # fields to get back
	endpointParams['access_token'] = params['access_token'] # access token

	return makeApiCall( url, endpointParams, 'GET' ) # make the api call

