{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/cloud-platform": {"description": "See, edit, configure, and delete your Google Cloud data and see the email address for your Google Account."}}}}, "basePath": "", "baseUrl": "https://publicca.googleapis.com/", "batchPath": "batch", "canonicalName": "Public Certificate Authority", "description": "The Public Certificate Authority API may be used to create and manage ACME external account binding keys associated with Google Trust Services' publicly trusted certificate authority. ", "discoveryVersion": "v1", "documentationLink": "https://cloud.google.com/public-certificate-authority/docs", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "publicca:v1alpha1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://publicca.mtls.googleapis.com/", "name": "publicca", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"projects": {"resources": {"locations": {"resources": {"externalAccountKeys": {"methods": {"create": {"description": "Creates a new ExternalAccount<PERSON><PERSON> bound to the project.", "flatPath": "v1alpha1/projects/{projectsId}/locations/{locationsId}/externalAccountKeys", "httpMethod": "POST", "id": "publicca.projects.locations.externalAccountKeys.create", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The parent resource where this external_account_key will be created. Format: projects/[project_id]/locations/[location]. At present only the \"global\" location is supported.", "location": "path", "pattern": "^projects/[^/]+/locations/[^/]+$", "required": true, "type": "string"}}, "path": "v1alpha1/{+parent}/externalAccountKeys", "request": {"$ref": "ExternalAccount<PERSON>ey"}, "response": {"$ref": "ExternalAccount<PERSON>ey"}, "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}}}}}}}}, "revision": "********", "rootUrl": "https://publicca.googleapis.com/", "schemas": {"ExternalAccountKey": {"description": "A representation of an ExternalAccountKey used for [external account binding](https://tools.ietf.org/html/rfc8555#section-7.3.4) within ACME.", "id": "ExternalAccount<PERSON>ey", "properties": {"b64MacKey": {"description": "Output only. Base64-URL-encoded HS256 key. It is generated by the PublicCertificateAuthorityService when the ExternalAccountKey is created", "format": "byte", "readOnly": true, "type": "string"}, "keyId": {"description": "Output only. Key ID. It is generated by the PublicCertificateAuthorityService when the ExternalAccountKey is created", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Resource name. projects/{project}/locations/{location}/externalAccountKeys/{key_id}", "readOnly": true, "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Public Certificate Authority API", "version": "v1alpha1", "version_module": true}