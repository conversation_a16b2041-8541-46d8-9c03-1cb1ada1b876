{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/content": {"description": "Manage your product listings and accounts for Google Shopping"}}}}, "basePath": "", "baseUrl": "https://merchantapi.googleapis.com/", "batchPath": "batch", "canonicalName": "Merchant", "description": "Programmatically manage your Merchant Center Accounts.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/merchant/api", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "merchantapi:lfp_v1beta", "kind": "discovery#restDescription", "mtlsRootUrl": "https://merchantapi.mtls.googleapis.com/", "name": "merchantapi", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accounts": {"resources": {"lfpInventories": {"methods": {"insert": {"description": "Inserts a `LfpInventory` resource for the given target merchant account. If the resource already exists, it will be replaced. The inventory automatically expires after 30 days.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpInventories:insert", "httpMethod": "POST", "id": "merchantapi.accounts.lfpInventories.insert", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The LFP provider account. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+parent}/lfpInventories:insert", "request": {"$ref": "LfpInventory"}, "response": {"$ref": "LfpInventory"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "lfpMerchantStates": {"methods": {"get": {"description": "Gets the LFP state of a merchant", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpMerchantStates/{lfpMerchantStatesId}", "httpMethod": "GET", "id": "merchantapi.accounts.lfpMerchantStates.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the state to retrieve. Format: `accounts/{account}/lfpMerchantStates/{target_merchant}`. For example, `accounts/123456/lfpMerchantStates/567890`.", "location": "path", "pattern": "^accounts/[^/]+/lfpMerchantStates/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+name}", "response": {"$ref": "LfpMerchantState"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "lfpSales": {"methods": {"insert": {"description": "Inserts a `LfpSale` for the given merchant.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpSales:insert", "httpMethod": "POST", "id": "merchantapi.accounts.lfpSales.insert", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The LFP provider account. Format: `accounts/{lfp_partner}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+parent}/lfpSales:insert", "request": {"$ref": "LfpSale"}, "response": {"$ref": "LfpSale"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}, "lfpStores": {"methods": {"delete": {"description": "Deletes a store for a target merchant.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpStores/{lfpStoresId}", "httpMethod": "DELETE", "id": "merchantapi.accounts.lfpStores.delete", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the store to delete for the target merchant account. Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`", "location": "path", "pattern": "^accounts/[^/]+/lfpStores/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+name}", "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "get": {"description": "Retrieves information about a store.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpStores/{lfpStoresId}", "httpMethod": "GET", "id": "merchantapi.accounts.lfpStores.get", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The name of the store to retrieve. Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`", "location": "path", "pattern": "^accounts/[^/]+/lfpStores/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+name}", "response": {"$ref": "LfpStore"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "insert": {"description": "Inserts a store for the target merchant. If the store with the same store code already exists, it will be replaced.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpStores:insert", "httpMethod": "POST", "id": "merchantapi.accounts.lfpStores.insert", "parameterOrder": ["parent"], "parameters": {"parent": {"description": "Required. The LFP provider account Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}}, "path": "lfp/v1beta/{+parent}/lfpStores:insert", "request": {"$ref": "LfpStore"}, "response": {"$ref": "LfpStore"}, "scopes": ["https://www.googleapis.com/auth/content"]}, "list": {"description": "Lists the stores of the target merchant, specified by the filter in `ListLfpStoresRequest`.", "flatPath": "lfp/v1beta/accounts/{accountsId}/lfpStores", "httpMethod": "GET", "id": "merchantapi.accounts.lfpStores.list", "parameterOrder": ["parent"], "parameters": {"pageSize": {"description": "Optional. The maximum number of `LfpStore` resources for the given account to return. The service returns fewer than this value if the number of stores for the given account is less than the `pageSize`. The default value is 250. The maximum value is 1000; If a value higher than the maximum is specified, then the `pageSize` will default to the maximum.", "format": "int32", "location": "query", "type": "integer"}, "pageToken": {"description": "Optional. A page token, received from a previous `ListLfpStoresRequest` call. Provide the page token to retrieve the subsequent page. When paginating, all other parameters provided to `ListLfpStoresRequest` must match the call that provided the page token. The token returned as nextPageToken in the response to the previous request.", "location": "query", "type": "string"}, "parent": {"description": "Required. The LFP partner. Format: `accounts/{account}`", "location": "path", "pattern": "^accounts/[^/]+$", "required": true, "type": "string"}, "targetAccount": {"description": "Required. The Merchant Center id of the merchant to list stores for.", "format": "int64", "location": "query", "type": "string"}}, "path": "lfp/v1beta/{+parent}/lfpStores", "response": {"$ref": "ListLfpStoresResponse"}, "scopes": ["https://www.googleapis.com/auth/content"]}}}}}}, "revision": "********", "rootUrl": "https://merchantapi.googleapis.com/", "schemas": {"CountrySettings": {"description": "Country-specific settings for the merchant.", "id": "CountrySettings", "properties": {"freeLocalListingsEnabled": {"description": "True if this merchant has enabled free local listings in MC.", "type": "boolean"}, "instockServingVerificationState": {"description": "Output only. The verification state of this merchant's instock serving feature.", "enum": ["VERIFICATION_STATE_UNSPECIFIED", "VERIFICATION_STATE_NOT_APPROVED", "VERIFICATION_STATE_IN_PROGRESS", "VERIFICATION_STATE_APPROVED"], "enumDescriptions": ["Verification state unspecified.", "Verification state not approved.", "Verification state in progress.", "Verification state approved."], "readOnly": true, "type": "string"}, "inventoryVerificationState": {"description": "Output only. The verification state of this merchant's inventory check.", "enum": ["VERIFICATION_STATE_UNSPECIFIED", "VERIFICATION_STATE_NOT_APPROVED", "VERIFICATION_STATE_IN_PROGRESS", "VERIFICATION_STATE_APPROVED"], "enumDescriptions": ["Verification state unspecified.", "Verification state not approved.", "Verification state in progress.", "Verification state approved."], "readOnly": true, "type": "string"}, "localInventoryAdsEnabled": {"description": "True if this merchant has enabled local inventory ads in MC.", "type": "boolean"}, "pickupServingVerificationState": {"description": "Output only. The verification state of this merchant's pickup serving feature.", "enum": ["VERIFICATION_STATE_UNSPECIFIED", "VERIFICATION_STATE_NOT_APPROVED", "VERIFICATION_STATE_IN_PROGRESS", "VERIFICATION_STATE_APPROVED"], "enumDescriptions": ["Verification state unspecified.", "Verification state not approved.", "Verification state in progress.", "Verification state approved."], "readOnly": true, "type": "string"}, "productPageType": {"description": "Output only. The product page type selected by this merchant.", "enum": ["PRODUCT_PAGE_TYPE_UNSPECIFIED", "GOOGLE_HOSTED", "MERCHANT_HOSTED", "MERCHANT_HOSTED_STORE_SPECIFIC"], "enumDescriptions": ["Product page type unspecified.", "Google hosted product page.", "Merchant hosted product page.", "Merchant hosted store specific product page."], "readOnly": true, "type": "string"}, "regionCode": {"description": "Required. The [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml) for the country for which these settings are defined.", "type": "string"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "InventoryStats": {"description": "The inventory statistics for a merchant.", "id": "InventoryStats", "properties": {"submittedEntries": {"description": "Number of entries (understanding entry as a pair of product and store) that were built based on provided inventories/sales and submitted to Google.", "format": "int64", "type": "string"}, "submittedInStockEntries": {"description": "Number of submitted in stock entries.", "format": "int64", "type": "string"}, "submittedProducts": {"description": "Number of products from provided inventories/sales that were created from matches to existing online products provided by the merchant or to the Google catalog.", "format": "int64", "type": "string"}, "unsubmittedEntries": {"description": "Number of entries that were built based on provided inventories/sales and couldn't be submitted to Google due to errors like missing product.", "format": "int64", "type": "string"}}, "type": "object"}, "LfpInventory": {"description": "Local Inventory for the merchant.", "id": "LfpInventory", "properties": {"availability": {"description": "Required. Availability of the product at this store. For accepted attribute values, see the [local product inventory data specification](https://support.google.com/merchants/answer/3061342)", "type": "string"}, "collectionTime": {"description": "Optional. The time when the inventory is collected. If not set, it will be set to the time when the inventory is submitted.", "format": "google-datetime", "type": "string"}, "contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "feedLabel": {"description": "Optional. The [feed label](https://developers.google.com/shopping-content/guides/products/feed-labels) for the product. If this is not set, it will default to `regionCode`.", "type": "string"}, "gtin": {"description": "Optional. The Global Trade Item Number of the product.", "type": "string"}, "name": {"description": "Output only. Identifier. The name for the `LfpInventory` resource. Format: `accounts/{account}/lfpInventories/{target_merchant}~{store_code}~{offer}`", "readOnly": true, "type": "string"}, "offerId": {"description": "Required. Immutable. A unique identifier for the product. If both inventories and sales are submitted for a merchant, this id should match for the same product. **Note**: if the merchant sells the same product new and used, they should have different IDs.", "type": "string"}, "pickupMethod": {"description": "Optional. Supported pickup method for this offer. Unless the value is \"not supported\", this field must be submitted together with `pickupSla`. For accepted attribute values, see the [local product inventory data specification](https://support.google.com/merchants/answer/3061342).", "type": "string"}, "pickupSla": {"description": "Optional. Expected date that an order will be ready for pickup relative to the order date. Must be submitted together with `pickupMethod`. For accepted attribute values, see the [local product inventory data specification](https://support.google.com/merchants/answer/3061342).", "type": "string"}, "price": {"$ref": "Price", "description": "Optional. The current price of the product."}, "quantity": {"description": "Optional. Quantity of the product available at this store. Must be greater than or equal to zero.", "format": "int64", "type": "string"}, "regionCode": {"description": "Required. The [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml) for the country where the product is sold.", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either the store code inserted through `InsertLfpStore` or the store code in the Business Profile.", "type": "string"}, "targetAccount": {"description": "Required. The Merchant Center ID of the merchant to submit the inventory for.", "format": "int64", "type": "string"}}, "type": "object"}, "LfpMerchantState": {"description": "The LFP state of a merchant.", "id": "LfpMerchantState", "properties": {"countrySettings": {"description": "Country-specific settings for the merchant.", "items": {"$ref": "CountrySettings"}, "type": "array"}, "inventoryStats": {"$ref": "InventoryStats", "description": "The inventory statistics for the merchant. The field will be absent if the merchant has no inventory submitted through LFP."}, "linkedGbps": {"description": "Number of [GBPs](https://www.google.com/business/) this merchant has access to.", "format": "int64", "type": "string"}, "name": {"description": "Identifier. The name of the `LfpMerchantState` resource. Format: `accounts/{account}/lfpMerchantStates/{target_merchant}`. For example, `accounts/123456/lfpMerchantStates/567890`.", "type": "string"}, "storeStates": {"description": "Output only. The state per store from the specified merchant. The field will be absent if the merchant has no stores submitted through LFP.", "items": {"$ref": "LfpStoreState"}, "readOnly": true, "type": "array"}}, "type": "object"}, "LfpSale": {"description": "A sale for the merchant.", "id": "LfpSale", "properties": {"contentLanguage": {"description": "Required. The two-letter ISO 639-1 language code for the item.", "type": "string"}, "feedLabel": {"description": "Optional. The [feed label](https://developers.google.com/shopping-content/guides/products/feed-labels) for the product. If this is not set, it will default to `regionCode`.", "type": "string"}, "gtin": {"description": "Required. The Global Trade Item Number of the sold product.", "type": "string"}, "name": {"description": "Output only. Identifier. The name of the `LfpSale` resource. Format: `accounts/{account}/lfpSales/{sale}`", "readOnly": true, "type": "string"}, "offerId": {"description": "Required. A unique identifier for the product. If both inventories and sales are submitted for a merchant, this id should match for the same product. **Note**: if the merchant sells the same product new and used, they should have different IDs.", "type": "string"}, "price": {"$ref": "Price", "description": "Required. The unit price of the product."}, "quantity": {"description": "Required. The relative change of the available quantity. Negative for items returned.", "format": "int64", "type": "string"}, "regionCode": {"description": "Required. The [CLDR territory code](https://github.com/unicode-org/cldr/blob/latest/common/main/en.xml) for the country where the product is sold.", "type": "string"}, "saleTime": {"description": "Required. The timestamp for the sale.", "format": "google-datetime", "type": "string"}, "storeCode": {"description": "Required. The identifier of the merchant's store. Either a `storeCode` inserted through the API or the code of the store in the Business Profile.", "type": "string"}, "targetAccount": {"description": "Required. The Merchant Center ID of the merchant to submit the sale for.", "format": "int64", "type": "string"}, "uid": {"description": "Output only. System generated globally unique ID for the `LfpSale`.", "readOnly": true, "type": "string"}}, "type": "object"}, "LfpStore": {"description": "A store for the merchant. This will be used to match to a store under the Google Business Profile of the target merchant. If a matching store can't be found, the inventories or sales submitted with the store code will not be used.", "id": "LfpStore", "properties": {"gcidCategory": {"description": "Optional. [Google My Business category id](https://gcid-explorer.corp.google.com/static/gcid.html).", "items": {"type": "string"}, "type": "array"}, "matchingState": {"description": "Optional. Output only. The state of matching to a Google Business Profile. See matchingStateHint for further details if no match is found.", "enum": ["STORE_MATCHING_STATE_UNSPECIFIED", "STORE_MATCHING_STATE_MATCHED", "STORE_MATCHING_STATE_FAILED"], "enumDescriptions": ["Store matching state unspecified.", "The `LfpStore` is successfully matched with a Google Business Profile store.", "The `LfpStore` is not matched with a Google Business Profile store."], "readOnly": true, "type": "string"}, "matchingStateHint": {"description": "Optional. Output only. The hint of why the matching has failed. This is only set when matchingState=`STORE_MATCHING_STATE_FAILED`. Possible values are: - \"`linked-store-not-found`\": There aren't any Google Business Profile stores available for matching. - \"`store-match-not-found`\": The provided `LfpStore` couldn't be matched to any of the connected Google Business Profile stores. Merchant Center account is connected correctly and stores are available on Google Business Profile, but the `LfpStore` location address does not match with Google Business Profile stores' addresses. Update the `LfpStore` address or Google Business Profile store address to match correctly. - \"`store-match-unverified`\": The provided `LfpStore` couldn't be matched to any of the connected Google Business Profile stores, as the matched Google Business Profile store is unverified. Go through the Google Business Profile verification process to match correctly.", "readOnly": true, "type": "string"}, "name": {"description": "Output only. Identifier. The name of the `LfpStore` resource. Format: `accounts/{account}/lfpStores/{target_merchant}~{store_code}`", "readOnly": true, "type": "string"}, "phoneNumber": {"description": "Optional. The store phone number in [E.164](https://en.wikipedia.org/wiki/E.164) format. Example: `+***********`", "type": "string"}, "placeId": {"description": "Optional. The [Google Place Id](https://developers.google.com/maps/documentation/places/web-service/place-id#id-overview) of the store location.", "type": "string"}, "storeAddress": {"description": "Required. The street address of the store. Example: 1600 Amphitheatre Pkwy, Mountain View, CA 94043, USA.", "type": "string"}, "storeCode": {"description": "Required. Immutable. A store identifier that is unique for the target merchant.", "type": "string"}, "storeName": {"description": "Optional. The merchant or store name.", "type": "string"}, "targetAccount": {"description": "Required. The Merchant Center id of the merchant to submit the store for.", "format": "int64", "type": "string"}, "websiteUri": {"description": "Optional. The website URL for the store or merchant.", "type": "string"}}, "type": "object"}, "LfpStoreState": {"description": "The state of a specific merchant's store.", "id": "LfpStoreState", "properties": {"matchingState": {"description": "Output only. The store matching state.", "enum": ["STORE_MATCHING_STATE_UNSPECIFIED", "STORE_MATCHING_STATE_MATCHED", "STORE_MATCHING_STATE_FAILED"], "enumDescriptions": ["Store matching state unspecified.", "The `LfpStore` is successfully matched with a Google Business Profile store.", "The `LfpStore` is not matched with a Google Business Profile store."], "readOnly": true, "type": "string"}, "matchingStateHint": {"description": "The hint of why the matching has failed (only set if matching_state is FAILED).", "type": "string"}, "storeCode": {"description": "Required. Immutable. The identifier of this store.", "type": "string"}}, "type": "object"}, "ListLfpStoresResponse": {"description": "Response message for the ListLfpStores method.", "id": "ListLfpStoresResponse", "properties": {"lfpStores": {"description": "The stores from the specified merchant.", "items": {"$ref": "LfpStore"}, "type": "array"}, "nextPageToken": {"description": "A token, which can be sent as `pageToken` to retrieve the next page. If this field is omitted, there are no subsequent pages.", "type": "string"}}, "type": "object"}, "Price": {"description": "The price represented as a number and currency.", "id": "Price", "properties": {"amountMicros": {"description": "The price represented as a number in micros (1 million micros is an equivalent to one's currency standard unit, for example, 1 USD = 1000000 micros).", "format": "int64", "type": "string"}, "currencyCode": {"description": "The currency of the price using three-letter acronyms according to [ISO 4217](http://en.wikipedia.org/wiki/ISO_4217).", "type": "string"}}, "type": "object"}, "ProductChange": {"description": "The change that happened to the product including old value, new value, country code as the region code and reporting context.", "id": "ProductChange", "properties": {"newValue": {"description": "The new value of the changed resource or attribute. If empty, it means that the product was deleted. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "oldValue": {"description": "The old value of the changed resource or attribute. If empty, it means that the product was created. Will have one of these values : (`approved`, `pending`, `disapproved`, ``)", "type": "string"}, "regionCode": {"description": "Countries that have the change (if applicable). Represented in the ISO 3166 format.", "type": "string"}, "reportingContext": {"description": "Reporting contexts that have the change (if applicable). Currently this field supports only (`SHOPPING_ADS`, `LOCAL_INVENTORY_ADS`, `YOUTUBE_SHOPPING`, `YOUTUBE_CHECKOUT`, `YOUTUBE_AFFILIATE`) from the enum value [ReportingContextEnum](/merchant/api/reference/rest/Shared.Types/ReportingContextEnum)", "enum": ["REPORTING_CONTEXT_ENUM_UNSPECIFIED", "SHOPPING_ADS", "DISCOVERY_ADS", "DEMAND_GEN_ADS", "DEMAND_GEN_ADS_DISCOVER_SURFACE", "VIDEO_ADS", "DISPLAY_ADS", "LOCAL_INVENTORY_ADS", "VEHICLE_INVENTORY_ADS", "FREE_LISTINGS", "FREE_LOCAL_LISTINGS", "FREE_LOCAL_VEHICLE_LISTINGS", "YOUTUBE_AFFILIATE", "YOUTUBE_SHOPPING", "CLOUD_RETAIL", "LOCAL_CLOUD_RETAIL", "PRODUCT_REVIEWS", "MERCHANT_REVIEWS", "YOUTUBE_CHECKOUT"], "enumDeprecated": [false, false, true, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false, false], "enumDescriptions": ["Not specified.", "[Shopping ads](https://support.google.com/merchants/answer/6149970).", "Deprecated: Use `DEMAND_GEN_ADS` instead. [Discovery and Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads](https://support.google.com/merchants/answer/13389785).", "[Demand Gen ads on Discover surface](https://support.google.com/merchants/answer/13389785).", "[Video ads](https://support.google.com/google-ads/answer/6340491).", "[Display ads](https://support.google.com/merchants/answer/6069387).", "[Local inventory ads](https://support.google.com/merchants/answer/3271956).", "[Vehicle inventory ads](https://support.google.com/merchants/answer/11544533).", "[Free product listings](https://support.google.com/merchants/answer/9199328).", "[Free local product listings](https://support.google.com/merchants/answer/9825611).", "[Free local vehicle listings](https://support.google.com/merchants/answer/11544533).", "[Youtube Affiliate](https://support.google.com/youtube/answer/13376398).", "[YouTube Shopping](https://support.google.com/merchants/answer/13478370).", "[Cloud retail](https://cloud.google.com/solutions/retail).", "[Local cloud retail](https://cloud.google.com/solutions/retail).", "[Product Reviews](https://support.google.com/merchants/answer/********).", "[Merchant Reviews](https://developers.google.com/merchant-review-feeds).", "YouTube Checkout ."], "type": "string"}}, "type": "object"}, "ProductStatusChangeMessage": {"description": "The message that the merchant will receive to notify about product status change event", "id": "ProductStatusChangeMessage", "properties": {"account": {"description": "The target account that owns the entity that changed. Format : `accounts/{merchant_id}`", "type": "string"}, "attribute": {"description": "The attribute in the resource that changed, in this case it will be always `Status`.", "enum": ["ATTRIBUTE_UNSPECIFIED", "STATUS"], "enumDescriptions": ["Unspecified attribute", "Status of the changed entity"], "type": "string"}, "changes": {"description": "A message to describe the change that happened to the product", "items": {"$ref": "ProductChange"}, "type": "array"}, "eventTime": {"description": "The time at which the event was generated. If you want to order the notification messages you receive you should rely on this field not on the order of receiving the notifications.", "format": "google-datetime", "type": "string"}, "expirationTime": {"description": "Optional. The product expiration time. This field will not be set if the notification is sent for a product deletion event.", "format": "google-datetime", "type": "string"}, "managingAccount": {"description": "The account that manages the merchant's account. can be the same as merchant id if it is standalone account. Format : `accounts/{service_provider_id}`", "type": "string"}, "resource": {"description": "The product name. Format: `accounts/{account}/products/{product}`", "type": "string"}, "resourceId": {"description": "The product id.", "type": "string"}, "resourceType": {"description": "The resource that changed, in this case it will always be `Product`.", "enum": ["RESOURCE_UNSPECIFIED", "PRODUCT"], "enumDescriptions": ["Unspecified resource", "Resource type : product"], "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Merchant API", "version": "lfp_v1beta", "version_module": true}